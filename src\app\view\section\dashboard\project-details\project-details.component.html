<div *ngIf="get_dashboard_data" class="d-flex justify-content-evenly gap-4">
  <div class="w-100" *ngFor="let stat of stats">
    <div class="card2">
      <div class="stats-card">
        <p class="box-head">{{ stat.label }}</p>
        <p class="box-text">{{ stat.value }}</p>
        <p class="box-sm">{{ stat.description }}</p>
      </div>
      <div class="btn-place">
        <button mat-fab class="stepper-action-btn">
          <mat-icon>{{ stat.icon }}</mat-icon>
        </button>
      </div>
    </div>
  </div>
</div>

<div *ngIf="recent_3_projects.length">
  <p class="header-text">Recent Projects</p>
  <div class="row m-2">
    <div class="col-4 my-2" *ngFor="let project of recent_3_projects; let i = index">
      <div class="active-project" (click)="selectProject(i)">
        <img class="project-image w-25" [src]="project.image" />
        <div class="project-active-details">
          <p class="font-weight-bold text-uppercase trimmer mb-2">
            {{ replaceCharacters(project.name) }}
          </p>
          <p class="mb-2">{{ project.date[0] }}</p>
          <p class="mb-2 trimmer">{{ project.city }}, {{ project.state }}, {{ project.country }}</p>
          <div
            class="progress-bar progress-bar-striped progress-bar-animated bg-success custom-progress"
            role="progressbar"
            [style.width]="calculateProgressWidth(project.status[0])">
            {{ calculateProgressWidth(project.status[0]) }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="project-container">
    <button mat-mini-fab class="prev" (click)="navigateTo(-1)">
      <mat-icon>keyboard_arrow_left</mat-icon>
    </button>
    <button mat-mini-fab class="next" (click)="navigateTo(1)">
      <mat-icon>keyboard_arrow_right</mat-icon>
    </button>
    <div class="project-card">
      <div class="horizontal-card">
        <div class="title">{{ project_number }}</div>
        <div class="project-details">
          <h3 class="text-uppercase trimmer">{{ replaceCharacters(recent_3_projects_name) }}</h3>
          <h3>{{ recent_3_projects_date }}</h3>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="title">
        <mat-icon class="icon m-0">stars</mat-icon>
        Workflow Status
      </div>
      <mat-vertical-stepper [selectedIndex]="currentStepIndex" [linear]="true">
        <mat-step
          *ngFor="let step of workflowSteps; let i = index"
          [completed]="i < currentStepIndex"
          [editable]="false">
          <ng-template matStepLabel>
            <div class="step-label d-flex align-items-center">
              {{ step }}
            </div>
          </ng-template>
        </mat-step>
      </mat-vertical-stepper>
      <div class="mt-2 d-flex justify-content-between gap-4" *ngIf="currentStepIndex === 3">
        <button mat-button [routerLink]="['/map']">Map</button>
        <button mat-button [routerLink]="['/app', 'analytics']">Analytics</button>
      </div>
    </div>
  </div>
</div>
