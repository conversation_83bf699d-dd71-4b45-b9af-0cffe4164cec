::ng-deep .mat-drawer-inner-container {
  background-color: var(--primary);
  border-right: 3px solid grey;
}
.sidenav-container {
  height: 100vh;
  width: 100%;
}
.sidenav {
  width: 70px;
  background-color: var(--primary);
}
@media (max-width: 767px) {
  .sidenav {
    width: 70px;
    position: fixed;
    z-index: var(--z-mobile-sidebar);
  }
  ::ng-deep .mat-drawer-container {
    background-color: var(--background);
  }
  ::ng-deep .mat-drawer-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
  }
}
.layout-container {
  height: 100vh;
  width: 100vw;
}
.mobile-sidebar-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-mobile-overlay);
  pointer-events: none;
}
.mobile-sidebar-wrapper.active {
  pointer-events: auto;
}
.mobile-overlay {
  position: fixed;
  top: 56px;
  left: 0;
  width: 100vw;
  height: calc(100vh - 56px);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-mobile-overlay);
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}
.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}
.mobile-sidebar {
  position: fixed;
  top: 56px;
  left: 0;
  height: calc(100vh - 56px);
  width: 70px;
  background: var(--primary);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  z-index: var(--z-mobile-sidebar);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}
.mobile-sidebar.open {
  transform: translateX(0);
}
.content-area {
  margin-left: 70px;
  padding-top: 64px;
  min-height: calc(100vh - 64px);
}
@media (max-width: 767px) {
  .content-area {
    margin-left: 0;
    padding-top: 56px;
    min-height: calc(100vh - 56px);
  }
}
