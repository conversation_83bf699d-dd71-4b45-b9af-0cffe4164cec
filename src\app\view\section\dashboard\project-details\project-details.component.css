.project-container {
  padding: 1rem;
  display: flex;
  gap: 1rem;
  width: 100%;
  position: relative;
}
.mat-stepper-vertical {
  background-color: var(--background) !important;
}
.active-project {
  width: 100%;
  display: flex;
  gap: 0.5rem;
  border-radius: 10px;
  background-color: #f2f2f2;
  padding: 10px;
  align-items: center;
  box-shadow: 0 3px 6px var(--shadow);
}
.project-image {
  height: 5.5rem;
  width: 5.5rem;
  object-fit: cover;
  border-radius: 10px;
  flex-shrink: 0;
}
.project-active-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.project-active-details p {
  margin: 0 0 0.4rem 0;
}
.project-card {
  flex: 1 1 50%;
}
.horizontal-card {
  height: 100%;
  width: 100%;
  border-radius: 1.25rem;
  background-color: var(--primary);
  padding: 3rem 1.25rem;
  color: var(--white);
  position: relative;
  overflow: hidden;
}
.horizontal-card .title {
  font-size: 6rem;
  font-weight: 900;
  color: rgba(187, 187, 187, 0.719);
  word-break: break-word;
}
.project-details {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
}
.project-details h3 {
  font-weight: 800;
}
.progress-bar {
  height: 1rem;
  border-radius: 1rem;
  font-size: 0.75rem;
}
.prev,
.next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}
.prev {
  left: 0;
}
.next {
  right: 0;
}
.card2 {
  position: relative;
  background-color: #f2f2f2;
  border-radius: 0.625rem;
  padding: 1rem;
  border-left: 5px solid var(--primary);
  box-shadow:
    0 0.5rem 1.5rem rgba(0, 0, 0, 0.1),
    0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}
.stats-card {
  flex: 1;
}
.box-head {
  font-size: 1.3rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}
.box-text {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}
.box-sm {
  font-size: 1rem;
  font-weight: 500;
}
.btn-place {
  position: relative;
}
.header-text {
  font-weight: 600;
  font-size: 1.5rem;
  text-align: center;
  margin-top: 1.5rem;
}
.title {
  font-weight: 600;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
@media (max-width: 767px) {
  .project-container {
    flex-direction: column;
    padding: 1rem 0.5rem;
  }
  .project-card,
  .horizontal-card {
    width: 100%;
    height: 175px;
  }
  .horizontal-card .title {
    font-size: 4rem;
    margin-bottom: 2rem;
  }
  .card2 {
    flex-direction: column;
    align-items: flex-start;
  }
  .btn-place {
    width: 100%;
    text-align: right;
    margin-top: 0.5rem;
  }
  .active-project {
    align-items: center;
    text-align: center;
  }
  .project-active-details {
    padding-top: 0.5rem;
    align-items: center;
  }
  .horizontal-card {
    padding: 2rem 1rem;
  }
}
