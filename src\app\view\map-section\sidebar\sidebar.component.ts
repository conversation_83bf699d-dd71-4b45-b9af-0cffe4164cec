import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { Drawtool, DrawtoolC } from '@/interfaces/drawtool';
import { ShareComponent } from '@/view/layout/share/share.component';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Subject, takeUntil } from 'rxjs';
import { ReportsComponent } from '../../layout/reports/reports.component';
import { HttpService } from '../services-map/http.service';

@Component({
  selector: 'sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit, OnChanges, OnDestroy {
  destroy$ = new Subject<void>();
  report_path = {};
  aoiData: Drawtool[] = null;
  project_id: number;
  drawtool: Drawtool = new DrawtoolC();
  inverter_value: any;
  currentMenu: string;
  previousID: string;
  p = 1;
  isShown1 = false;
  datesumlength: any;
  datevalue: any;
  Project_layer_inverter_data: any;
  Project_layer: any;
  Project_layer_summary: any;
  defects_summary = [];
  summary_data = [];
  summ: any;
  invData = [];
  inv_all: any;
  inv_tb = [];
  Summary_tab: any;
  sub_defect = [];
  sub_label = [];
  kml_file_location: any;
  inverterMap = new Map();
  menuIcons = [
    {
      name: 'PID',
      img: './assets/images/pid.svg',
      color: '#DC2828',
    },
    {
      name: 'Hotspot',
      img: './assets/images/hotspot.svg',
      color: '#089E60',
    },
    {
      name: 'Open Circuit',
      img: './assets/images/open circuit.svg',
      color: '#FFC107',
    },
    {
      name: 'Panel Failure',
      img: './assets/images/panel failure.svg',
      color: '#D56DFC',
      rippleColor: '#007bff45',
    },
    {
      name: 'Short Circuit',
      img: './assets/images/short circuit.svg',
      color: '#1396CC',
    },
    {
      name: 'Diode Issue',
      img: './assets/images/open circuit.svg',
      color: '#FFC107',
    },
    {
      name: 'Missing Module',
      img: './assets/images/Missing_Module.svg',
      color: '#800080',
    },
    {
      name: 'Broken Glass',
      img: './assets/images/broken_glass.svg',
      color: '#FF4500',
    },
    {
      name: 'Delamination',
      img: './assets/images/Delimination.svg',
      color: '#00008B',
    },
  ];
  descriptionMap: { [key: string]: string } = {
    Hotspot: 'Power dissipation occurring in a small area results in cell overheating',
    'Short Circuit':
      'One or more substring open circuit failure with hotspot. At one or more substrings, easily mistaken for cell breakage or cell defects, Potential Induced Degradation (PID), or mismatch',
    'Open Circuit': 'Loss of connection within module junction box or cell connecter',
    'Diode Issue': 'Loss of connection within module junction box or cell connecter',
    PID: 'The full panel surface is homogeneously heated up compared to other panels. It may happen due to PID effects.',
  };
  defaultDescription: string =
    'Frames of the modules are homogeneously heated. The negative grounding should be checked at the inverter level. The module frames would have high leakage current.';
  severityMapping = {
    Hotspot: 'High',
    Bypass_Diode_Issues: 'Severe',
    Junction_Box_Issue: 'Severe',
    Dirt: 'Low',
    Multicell_Hotspot: 'High',
    Single_Cell_Hotspot: 'Severe',
    Short_Circuit: 'Medium',
    Open_Circuit: 'High',
    Diode_Issue: 'Severe',
    Panel_Failure: 'High',
    Open_String_Tables: 'Severe',
    String_Failure: 'Severe',
    PID: 'High',
    Missing_Module: 'High',
    Broken_Glass: 'High',
    Delamination: 'Medium',
    // For old projects
    Module: 'High',
    Table: 'Severe',
    String: 'Severe',
  };
  category: string = null;
  currentIndex = 0;
  isOpenCompare: boolean;
  dialogRef: any;
  namedata: string;
  slider_state = false;
  colors_tab = [];
  Completed_date_array = [];
  inverter_names = [];
  inverter_names2 = [];
  topograpyData: any = '';
  topography_title: any;
  topography_values: any;
  grading_values: any;
  topography_data: any;
  vegetation_values: any;
  isVisible = false;
  sidebar_logo = '';
  closesidebar: any;
  splitted_sidebar_value: any;
  grading_title: any;
  grading_data: any;
  vegetation_data: any;
  usersWithAccess: [] = [];
  usersList: [] = [];
  Cadastral_data: any;
  compare_icon = false;
  graph_pie_defects: any = null;
  selectedRow: string | null = null;
  hoveredRow: string | null = null;
  selectedTable: string = '';
  currentIndex2: number = 0;
  cadastralData: any;
  cadastral_present: boolean = false;
  current_summary_state: any = 'PID';
  inverter_page: any;
  cadastrial_map_page: any;
  removing_kml: any = 'remove';
  subdefects_page: any = 'visible';
  activeCardIndex: number | null = null;
  @Input() booleanValueFromParent: boolean;
  @Output() current_summary_state_event: EventEmitter<any> = new EventEmitter<any>();
  @Output() inverter_page_event: EventEmitter<any> = new EventEmitter<any>();
  @Output() cadastrial_map_page_event: EventEmitter<any> = new EventEmitter<any>();
  @Output() removing_kml_event: EventEmitter<any> = new EventEmitter<any>();
  @Output() subdefects_page_event: EventEmitter<any> = new EventEmitter<any>();
  @Output() close_stack: EventEmitter<any> = new EventEmitter<any>();
  @Output() aoi_action_event = new EventEmitter<any>();

  constructor(
    private _http: HttpService,
    public dialog: MatDialog,
    private userService: UserService,
    private toastr: ToastrService,
    private sharedDataService: SharedDataService,
    private apiConfigService: ApiConfigService
  ) {}

  ngOnInit(): void {
    this.userService.sidebarLogo$.pipe(takeUntil(this.destroy$)).subscribe((logo: string) => {
      this.sidebar_logo = logo;
    });
    this.loadAdminUserList();
    this.getSidebarStatus();
    this.loadProjectData();
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['booleanValueFromParent']?.currentValue === true) {
      this.updateInverterData();
    }
  }
  private loadAdminUserList(): void {
    const role = this.userService.getRole();
    const group_id = this.userService.getGroupId();
    if (role === 'admin' && group_id) {
      this.sharedDataService
        .fetchData(this.apiConfigService.getManageUsers(group_id), 'manage_users')
        .pipe(takeUntil(this.destroy$))
        .subscribe(data => {
          if (data) this.usersList = data;
        });
    }
  }
  private getSidebarStatus(): void {
    this._http
      .getclosesidebar()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: info => {
          this.closesidebar = info;
          const [id, status] = info.close_side_bar.split('/', 2);
          if (status === 'True') this.closeSidebar(id);
        },
        error: err => {
          this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
          console.error(err);
        },
      });
  }
  private loadProjectData(): void {
    const project_id = localStorage.getItem('project_id');
    this.sharedDataService
      .fetchData(this.apiConfigService.getProjectData(project_id), `project_id_${project_id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.inv_all = data;
        this.extractCompletedDates(data['date_status']);
        this.populateProjectData(data);
      });
  }
  private extractCompletedDates(dateStatus: any): void {
    const keys = Object.keys(dateStatus);
    this.Completed_date_array = keys.filter(k => dateStatus[k] === 'completed');
    this.datesumlength = this.Completed_date_array.length - 1;
  }
  private populateProjectData(data: any): void {
    this.datevalue = localStorage.getItem('date');
    const obj_date_value = data['processed_data'][this.datevalue];
    this.project_id = data.id;
    this.kml_file_location = obj_date_value['kml_file_location'];
    this.usersWithAccess = data['users'];
    this.report_path = obj_date_value['report_path'];
    this.Project_layer_summary = Object.keys(obj_date_value['summary_layers']);
    this.Project_layer_inverter_data = Object.keys(obj_date_value['inverter_layers']);
    this.category = data['category'].toLowerCase();
    this.compare_icon = ['thermography', 'topography'].includes(this.category);
    this.topography_data = Object.keys(obj_date_value['topography_layers']);
    const cadastralMap = obj_date_value['topography_layers']['Cadastral Map'];
    if (cadastralMap && Object.keys(cadastralMap).length > 0) {
      this.Cadastral_data = Object.keys(cadastralMap);
      this.cadastral_present = true;
    }
    this.grading_data = Object.keys(obj_date_value['grading_layers']);
    if (obj_date_value['vegetation_layers']) {
      this.vegetation_data = Object.keys(obj_date_value['vegetation_layers']);
    }
    this.inverter_names = this.Project_layer_inverter_data.map((name, i) => ({
      name,
      pvalue: i + 1,
    }));
    // this.inverterNameMap(this.inverter_names);
  }
  private updateInverterData(): void {
    this.datevalue = localStorage.getItem('date');
    const obj_date_value = this.inv_all['processed_data'][this.datevalue];
    this.Project_layer_inverter_data = Object.keys(obj_date_value['inverter_layers']);
    this.Project_layer = Object.values(obj_date_value['inverter_layers']);
    this.inverterNameMap(this.inverter_names);
  }
  inverterNameMap(inverter_names: any[]): void {
    inverter_names.forEach(inv => this.LoadGBKml(parseInt(inv.pvalue)));
  }
  formatSummaryName(name: string): string {
    return name.replace(/\s+/g, '_');
  }
  activateMenu(id: string): void {
    if (this.previousID && (this.Summary_tab || this.Summary_tab === undefined)) {
      const prevId = document.getElementById(this.previousID);
      if (prevId) prevId.style.width = '0px';
    } else {
      const e = document.getElementById(id);
      if (e) {
        e.style.width = '5px';
        this.previousID = id;
      }
    }
    if (id === 'compare') {
      this.close_stack.emit();
    }
  }
  initSlider(isNext: boolean): void {
    const iconsList = document.querySelector('.slider-wrapper .icon-section') as HTMLElement;
    const direction = isNext ? 1 : -1;
    const scrollAmount = iconsList.clientWidth * direction;
    iconsList.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  }
  openSidebar(id: string, menuId: string): void {
    this.setSidebarVisible(id);
    this.namedata = '';
    this.currentMenu = menuId;
    const menuHandlers = {
      summary: () => {
        this.summary_data = [];
        this.summary_data_render();
      },
      inverter: () => {
        this.invData = [];
        this.inverter_data_render();
      },
      Topography: () => {
        this.topograpyData = [];
        this.Topography_data_render();
      },
      Grading: () => {
        this.topograpyData = [];
        this.Grading_data_render();
      },
      Cadastral: () => {
        this.cadastral_data_render();
      },
      Vegetation: () => {
        this.topograpyData = [];
        this.Vegetation_data_render();
      },
      aoi: () => {
        this.getDrawtools();
      },
    };
    if (menuHandlers[menuId]) menuHandlers[menuId]();
  }
  private setSidebarVisible(id: string): void {
    const sideBar = document.getElementById(id);
    if (!sideBar) return;
    const setStyles = () => {
      const isMobile = window.innerWidth < 700;
      sideBar.style.display = 'block';
      sideBar.style.width = isMobile ? '250px' : '325px';
      sideBar.style.zIndex = '1000';
    };
    setStyles();
    window.addEventListener('resize', setStyles);
  }
  closeSidebar(id: string): void {
    this.selectedRow = this.selectedTable = this.activeCardIndex = null;
    const sideBar = document.getElementById(id);
    if (sideBar) {
      sideBar.style.display = 'none';
      sideBar.style.width = '0px';
    }
    this.removing_kml_event.emit('chevron_left');
    if (this.Summary_tab) {
      this.activateMenu(this.previousID);
      this.Summary_tab = false;
    }
    this.aoi_action_event.emit({ action: 'remove', data: {} });
  }
  activate_tab(name: string, i: number, currentMenu: string, defect_name: string, kml: any): void {
    localStorage.setItem('current_tab', defect_name);
    this.namedata = defect_name;
    this.currentIndex = this.currentIndex2 = i;
    this.selectedTable = null;
    const kml_color: string[] = [];
    if (currentMenu === 'summary') {
      this.loadSumm_data(defect_name);
      const summary = this.summ['processed_data'][this.datevalue]['summary_layers'][name];
      kml_color.push(...this.extractColorsFromGroup(summary));
      this.emitSummaryState(summary['kml'], currentMenu, kml_color);
    }
    if (currentMenu === 'inverter') {
      this.inv_table(this.inverter_value, kml);
      const inverter =
        this.inv_all['processed_data'][this.datevalue]['inverter_layers'][this.inverter_value][
          name
        ];
      kml_color.push(...this.extractColorsFromGroup(inverter));
      this.emitSummaryState(inverter['kml'], currentMenu, kml_color);
    }
  }
  private extractColorsFromGroup(group: any): string[] {
    if (!group.sub_group || Object.keys(group.sub_group).length === 0) {
      return [group.color];
    }
    return Object.values(group.sub_group).map((sg: any) => sg.color);
  }
  private emitSummaryState(kml: any, menu: string, color: string[]): void {
    this.current_summary_state = { tab: kml, menu, color, pageno: this.p };
    this.current_summary_state_event.emit(this.current_summary_state);
  }
  sub_defect_kml(n: string, color: string, type: string): void {
    localStorage.setItem('sub_defects', n);
    this.selectedRow = this.selectedTable = n;
    const base = {
      tab: n,
      color,
      pageno: this.p,
    };
    switch (type) {
      case 'summary': {
        const summaryKml =
          this.summ['processed_data'][this.datevalue]['summary_layers'][this.namedata]['sub_group'][
            n
          ]['kml'];
        this.emitSummaryState(summaryKml, 'summary_sub_details', [summaryKml.color]);
        break;
      }
      case 'inverter': {
        const inverterKml =
          this.inv_all['processed_data'][this.datevalue]['inverter_layers'][this.inverter_value][
            this.namedata
          ]['sub_group'][n]['kml'];
        this.emitSummaryState(inverterKml, 'inverter_sub_details', [inverterKml.color]);
        break;
      }
      case 'Topography':
        this.emitCadastralMapEvent('cadastral_map', base);
        break;
      case 'Grading':
      case 'Vegetation':
      case 'cadastral':
        this.emitCadastralMapEvent(type, base);
        break;
    }
  }
  private emitCadastralMapEvent(menu: string, data: any): void {
    this.cadastrial_map_page = { ...data, menu };
    this.cadastrial_map_page_event.emit(this.cadastrial_map_page);
  }
  private getProjectId(): string {
    return localStorage.getItem('project_id') || '';
  }
  private getDateValue(): string {
    return localStorage.getItem('date') || '';
  }
  private fetchProcessedData(callback: (data: any) => void): void {
    const projectId = this.getProjectId();
    this.sharedDataService
      .fetchData(this.apiConfigService.getProjectData(projectId), `project_id_${projectId}`)
      .subscribe(callback, err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        console.error(err);
      });
  }
  private handleSummaryTabSwitch(
    targetMenu: string,
    retryFn: () => void,
    overridePrevID?: string
  ): boolean {
    if (this.Summary_tab) {
      const idCheck = ['summary', 'inverter', 'Topography'].includes(this.previousID);
      if (idCheck) {
        this.Summary_tab = false;
        this.activateMenu(targetMenu);
        retryFn();
        return true;
      }
      this.Summary_tab = false;
      this.previousID = overridePrevID || targetMenu;
      this.closeSidebar('summarySidebar');
      return true;
    }
    return false;
  }
  summary_data_render() {
    this.removing_kml_event.emit(this.removing_kml);
    localStorage.setItem('current_tab', 'PID');
    if (this.handleSummaryTabSwitch('summary', this.summary_data_render.bind(this))) return;
    this.fetchProcessedData(data => {
      this.summ = data;
      this.datevalue = this.getDateValue();
      const summaryLayer = this.summ['processed_data'][this.datevalue]['summary_layers'];
      this.Project_layer_summary = Object.keys(summaryLayer);
      this.summary_data = this.Project_layer_summary.map(name => ({
        name,
        kml: summaryLayer[name]['kml'],
        color: summaryLayer[name]['color'],
        count: summaryLayer[name]['Count'],
      }));
      const first = this.summary_data[0];
      this.activate_tab(first.name, 0, 'summary', first.name, first.kml);
    });
  }
  loadSumm_data(elm: string): void {
    this.removing_kml_event.emit(this.removing_kml);
    this.subdefects_page_event.emit('visibility_off');
    const layerData =
      this.summ['processed_data'][this.datevalue]['summary_layers'][elm]['sub_group'];
    if (['Hotspot', 'Panel Failure'].includes(elm)) {
      this.sub_defect = [];
      this.sub_label = [];
    }
    this.defects_summary = [];
    this.sub_defect = [];
    this.sub_label = [];
    this.colors_tab = [];
    for (const key in layerData) {
      const item = layerData[key];
      this.defects_summary.push({
        name: key,
        count: item['Count'],
        kml: item['kml'],
        color: item['color'],
      });
      this.sub_label.push(key);
      this.sub_defect.push(item['Count']);
      this.colors_tab.push(item['color']);
    }
    const chartElement = document.getElementById('chart1');
    if (chartElement) {
      if (this.sub_defect.length === 0) {
        chartElement.style.display = 'none';
        this.sub_defect = [0, 0, 0, 0, 0];
        this.sub_label = ['No data', 'No data', 'No data', 'No data', 'No data'];
      } else {
        chartElement.style.display = 'flex';
      }
    }
    this.graph_pie_defects = {
      data: [
        {
          values: this.sub_defect,
          labels: this.sub_label,
          type: 'pie',
          showlegend: false,
          hoverinfo: 'label+percent',
          textinfo: 'label+percent',
          textposition: 'inside',
          hole: 0.6,
          marker: { colors: this.colors_tab },
        },
      ],
      layout: { margin: { r: 20, t: 20, l: 20, b: 20 }, height: 200, width: 200 },
      config: {
        responsive: true,
        annotations: [{ showarrow: false, x: 0.17, y: 0.5 }],
        displayModeBar: true,
        displaylogo: false,
      },
    };
    this.Summary_tab = true;
  }
  LoadGBKml(value: number): void {
    const padded = value < 10 ? `0${value}` : `${value}`;
    const url = `${this.kml_file_location}INVERTER${padded}/gb.kml`;
    fetch(url)
      .then(res => res.text())
      .then(kmlText => {
        const parser = new DOMParser();
        const kml = parser.parseFromString(kmlText, 'text/xml');
        const description = kml.querySelector('Placemark description')?.textContent || '';
        const inverterName = description.split('Inverter No: ')[1]?.trim();
        if (inverterName) {
          this.inverterMap.set(inverterName, value);
        }
      });
  }
  inverter_data_render() {
    this.removing_kml_event.emit(this.removing_kml);
    localStorage.setItem('current_tab', 'PID');
    if (this.handleSummaryTabSwitch('inverter', this.inverter_data_render.bind(this))) return;
    this.fetchProcessedData(data => {
      this.inv_all = data;
      this.datevalue = this.getDateValue();
      const inverterLayers = data['processed_data'][this.datevalue]['inverter_layers'];
      this.Project_layer_inverter_data = Object.keys(inverterLayers);
      this.Project_layer = Object.values(inverterLayers);
      this.inverter_names = this.Project_layer_inverter_data.map((name, i) => ({
        name,
        pvalue: i + 1,
      }));
      this.inverter_value = this.inverter_names[0]?.name;
      this.load_invDiv();
    });
  }
  load_invDiv(): void {
    this.invData = [];
    const inverter = this.inverter_names.find(i => i.name === this.inverter_value);
    if (inverter) {
      this.p = this.inverterMap.get(this.inverter_value) ?? parseInt(inverter.pvalue);
    }
    this.removing_kml_event.emit(this.removing_kml);
    this.inverter_page_event.emit(this.p);
    this.subdefects_page_event.emit('visibility_off');
    const obj =
      this.inv_all['processed_data'][this.datevalue]['inverter_layers'][this.inverter_value];
    for (const k in obj) {
      const item = obj[k];
      this.invData.push({
        inv_name: this.inverter_value,
        name: k,
        count: item['count'],
        kml: item['kml'],
        color: item['color'],
      });
    }
    const first = this.invData[0];
    this.activate_tab(first.name, 0, 'inverter', first.name, first.kml);
    this.inv_table(this.inverter_value, this.namedata);
  }
  inv_table(ele: string, elm: string): void {
    this.inv_tb = [];
    const subGroups =
      this.inv_all['processed_data'][this.datevalue]['inverter_layers'][ele][elm]['sub_group'];
    for (const key in subGroups) {
      const item = subGroups[key];
      this.inv_tb.push({
        name: key,
        count: item['count'],
        kml: item['kml'],
        color: item['color'],
      });
    }
    this.Summary_tab = true;
  }
  Topography_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (
      this.handleSummaryTabSwitch('Topography', this.Topography_data_render.bind(this), 'inverter')
    )
      return;
    this.fetchProcessedData(data => {
      this.summ = data;
      this.datevalue = this.getDateValue();
      const layers = data['processed_data'][this.datevalue]['topography_layers'];
      this.Project_layer = layers;
      this.topography_title = Object.keys(layers);
      const featureData =
        layers['TopographicFeatures']?.sub_feature || layers['Topography']?.sub_feature;
      if (!featureData) {
        console.error('Sub-feature not found in JSON data');
        return;
      }
      this.topography_values = Object.keys(featureData).map(name => ({
        name,
        kml: featureData[name].kml,
        color: featureData[name].color,
      }));
      const first = this.topography_values[0];
      this.current_summary_state = {
        tab: first.name,
        menu: 'cadastral_map',
        color: first.color,
        pageno: 1,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
      this.Summary_tab = true;
    });
  }
  Grading_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (localStorage.getItem('shareComponent') === 'open') {
      this.Summary_tab = false;
      this.dialog.closeAll();
      this.activateMenu('Grading');
    }
    if (this.handleSummaryTabSwitch('Grading', this.Grading_data_render.bind(this), 'inverter'))
      return;
    this.fetchProcessedData(data => {
      this.summ = data;
      this.datevalue = this.getDateValue();
      const layers = data['processed_data'][this.datevalue]['grading_layers'];
      this.Project_layer = layers;
      this.grading_title = Object.keys(layers);

      const sub_feature = layers['GradingFeatures']?.sub_feature;
      this.grading_values = Object.keys(sub_feature).map(name => ({
        name,
        kml: sub_feature[name].kml,
        color: sub_feature[name].color,
      }));

      const first = this.grading_values[0];
      this.current_summary_state = {
        tab: first.name,
        menu: 'Grading',
        color: first.color,
        pageno: 1,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
      this.Summary_tab = true;
    });
  }
  cadastral_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (this.handleSummaryTabSwitch('Cadastral', this.cadastral_data_render.bind(this), 'inverter'))
      return;
    this.fetchProcessedData(data => {
      this.summ = data;
      this.datevalue = this.getDateValue();
      const sub_feature =
        data['processed_data'][this.datevalue]['topography_layers']?.['Cadastral Map']?.sub_feature;
      if (!sub_feature) {
        console.error('Error accessing sub_feature');
        return;
      }
      this.cadastralData = Object.keys(sub_feature).map(name => ({
        name,
        kml: sub_feature[name].kml,
        color: sub_feature[name].color,
      }));
      const first = this.cadastralData[0];
      this.current_summary_state = {
        tab: first.name,
        menu: 'Cadastral',
        color: first.color,
        pageno: 1,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
      this.Summary_tab = true;
    });
  }
  Vegetation_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (localStorage.getItem('shareComponent') === 'open') {
      this.Summary_tab = false;
      this.dialog.closeAll();
      this.activateMenu('Vegetation');
    }
    if (
      this.handleSummaryTabSwitch('Vegetation', this.Vegetation_data_render.bind(this), 'inverter')
    )
      return;
    this.fetchProcessedData(data => {
      this.summ = data;
      this.datevalue = this.getDateValue();
      const layers = data['processed_data'][this.datevalue]['vegetation_layers'];
      const sub_feature = layers['Vegetation Features']?.sub_feature;
      this.vegetation_values = Object.keys(sub_feature).map(name => ({
        name,
        kml: sub_feature[name].kml,
        color: sub_feature[name].color,
      }));
      const first = this.vegetation_values[0];
      this.current_summary_state = {
        tab: first.name,
        menu: 'Vegetation',
        color: first.color,
        pageno: 1,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
      this.Summary_tab = true;
    });
  }
  openSharedialog() {
    const project_name = localStorage.getItem('project_name');
    const project_id = localStorage.getItem('project_id');
    const dialogRef = this.dialog.open(ShareComponent, {
      data: {
        projectName: project_name,
        projectId: project_id,
        usersWithAccess: this.usersWithAccess,
        allUsers: this.usersList,
      },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.sharedDataService.remove(`project_id_${project_id}`);
        this.ngOnInit();
      }
    });
  }
  openReportdialog() {
    const project_name = localStorage.getItem('project_name');
    const date = localStorage.getItem('date');
    this.dialog.open(ReportsComponent, {
      data: {
        projectName: project_name,
        currentDate: date,
        fileObj: this.report_path,
      },
      disableClose: true,
    });
  }
  getDrawtools() {
    this.apiConfigService.getDrawtools(this.project_id, this.datevalue).subscribe(
      (res: any) => {
        if (res['status'] == 'success') {
          this.aoiData = res.data;
        }
      },
      err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        console.error(err);
      }
    );
  }
  createDrawtool(drawtool) {
    this.apiConfigService.createDrawtool(drawtool).subscribe(
      (res: any) => {
        if (res['status'] == 'success') {
          this.toastr.success('', 'AOI created successfully');
          this.getDrawtools();
          this.aoi_action_event.emit({ action: 'show', data: drawtool });
        }
      },
      err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        console.error(err);
      }
    );
  }
  updateDrawtool(drawtool) {
    this.apiConfigService.updateDrawtool(drawtool).subscribe(
      (res: any) => {
        if (res['status'] == 'success') {
          this.toastr.success('', 'AOI updated successfully');
          this.aoi_action_event.emit({ action: 'show', data: drawtool });
        }
      },
      err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        console.error(err);
      }
    );
  }
  deleteDrawtool(drawtool) {
    this.activeCardIndex = null;
    this.apiConfigService.deleteDrawtool(drawtool.id).subscribe(
      (res: any) => {
        if (res['status'] == 'success') {
          this.aoi_action_event.emit({ action: 'remove', data: drawtool });
          this.toastr.success('', 'AOI updated successfully');
          this.getDrawtools();
        }
      },
      err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        console.error(err);
      }
    );
  }
  selectCard(index: number): void {
    this.activeCardIndex = index;
  }
  showDrawtool(drawtool, i) {
    this.selectCard(i);
    this.aoi_action_event.emit({ action: 'show', data: drawtool });
  }
  editDrawtool(drawtool, i) {
    this.selectCard(i);
    this.aoi_action_event.emit({ action: 'edit', data: drawtool });
  }
  addDrawtool() {
    this.activeCardIndex = null;
    this.aoi_action_event.emit({ action: 'add', data: {} });
  }
  downloadDrawtool(drawtool, i) {
    this.selectCard(i);
    const label = drawtool.label.trim();
    const polygon = Array.isArray(drawtool.polygon) ? drawtool.polygon : [drawtool.polygon];
    const coordinates = polygon.map(point => `${point.lng},${point.lat}`).join('\n');

    const kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <Placemark>
      <name>${label}</name>
      <description>${drawtool.description}</description>
      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>
              ${coordinates}
            </coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
    </Placemark>
  </Document>
</kml>`;

    const blob = new Blob([kml], { type: 'application/vnd.google-earth.kml+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${label}.kml`;
    a.click();
    if (a.parentNode) {
      a.parentNode.removeChild(a);
    }
    URL.revokeObjectURL(url);
  }
}
