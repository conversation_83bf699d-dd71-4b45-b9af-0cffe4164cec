import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { ProfileModalComponent } from '../profile-modal/profile-modal.component';

@Component({
  selector: 'app-manageuser',
  templateUrl: './manageuser.component.html',
  styleUrls: ['./manageuser.component.css'],
})
export class ManageuserComponent implements OnInit {
  displayedColumns: string[] = ['name', 'email', 'role', 'active', 'created_on', 'edit'];
  dataSource = new MatTableDataSource<any>([]);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  roles: [] = [];

  constructor(
    private apiConfigService: ApiConfigService,
    private sharedDataService: SharedDataService,
    private dialog: MatDialog,
    private userService: UserService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    const role = this.userService.getRole();
    const group_id = this.userService.getGroupId();

    if (role === 'admin' && group_id) {
      this.sharedDataService
        .fetchData(this.apiConfigService.getManageUsers(group_id), 'manage_users')
        .subscribe(data => {
          if (data) {
            setTimeout(() => {
              this.dataSource.data = data;
              this.dataSource.paginator = this.paginator;
              this.dataSource.sort = this.sort;
            }, 200);
            this.sharedDataService
              .fetchData(this.apiConfigService.getRoles(), 'roles')
              .subscribe(res => {
                this.roles = res;
              });
          }
        });
    }
  }
  openDialog(user) {
    const dialogRef = this.dialog.open(ProfileModalComponent, {
      data: { user: user, role: this.roles },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.dataModified) {
          this.sharedDataService.remove('manage_users');
          this.ngOnInit();
        }
      }
    });
  }
  toggleUser(user) {
    const data = {
      id: user.id,
      enabled: user.enabled,
      email: user.email,
    };
    this.apiConfigService.activeAccess(data).subscribe(
      (res: any) => {
        if (res.status == 'success') {
          this.toastr.success(res.message);
          this.sharedDataService.remove('manage_users');
          this.ngOnInit();
        }
      },
      err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
      }
    );
  }
}
