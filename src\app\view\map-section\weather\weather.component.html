<div id="weather" *ngIf="weatherData && myObject">
  <div id="header">
    <i class="fa-solid fa-temperature-half mr-2"></i>
    Current Weather
  </div>
  <div id="header2" class="my-1 font-weight-bold">
    <span>{{ weatherData.date }} - {{ weatherData.time }}</span>
    <span style="color: var(--primaryBg)">{{ weatherData.city }}, {{ weatherData.country }}</span>
  </div>
  <div id="header3">
    <div class="d-flex justify-content-center align-items-center">
      <span title="Toggle Unit" class="pointer" (click)="toggleUnit()">
        <img [src]="weather_img" style="max-width: 80px" />
      </span>
      <p style="font-size: 50px" class="ml-4 m-0">
        {{ unit == 'C' ? weatherData.temperatureC : weatherData.temperatureF }}
        <span id="temp-unit">°{{ unit }}</span>
      </p>
    </div>
  </div>
  <div id="header4" class="my-1 text-center">
    <div *ngFor="let item of myObject | keyvalue" id="header4-part">
      <p class="value1 m-0">{{ item.value }}</p>
      <p class="key1 m-0">{{ item.key }}</p>
    </div>
  </div>
</div>
