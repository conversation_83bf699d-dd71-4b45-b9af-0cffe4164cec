import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'project-details',
  templateUrl: './project-details.component.html',
  styleUrls: ['./project-details.component.css'],
})
export class ProjectDetailsComponent implements OnInit {
  @Input() get_dashboard_data: any;
  @Input() main_data: any[] = [];
  recent_3_projects: any[] = [];
  slideIndex = 1;
  recent_3_projects_name = '';
  recent_3_projects_date = '';
  project_number = '';
  workflow_status = 'created';
  stats = [];
  workflowSteps: any[] = [
    'Project Creation',
    'Data Acquisition',
    'Data Processing',
    'Analytics & Visualization',
  ];

  ngOnInit(): void {
    this.processData();
    this.stats = [
      {
        label: 'Total capacity scanned',
        description: 'Inspected till date',
        value: this.get_dashboard_data.plant_size_scanned,
        icon: 'widgets',
      },
      {
        label: 'Total Net Power Loss',
        description: 'Estimated Power Loss',
        value: this.get_dashboard_data.total_power_loss,
        icon: 'flash_on',
      },
      {
        label: 'Total Number of Issues',
        description: 'Issues detected',
        value: this.get_dashboard_data.total_defects,
        icon: 'device_hub',
      },
    ];
  }

  replaceCharacters(value: string): string {
    return value.toUpperCase().replace(/[_-]/g, ' ');
  }

  private processData(): void {
    if (this.main_data.length) {
      this.recent_3_projects = this.main_data.map(row => ({
        ...row,
        date: Object.keys(row['date_status']),
        status: Object.values(row['date_status']),
      }));
      this.updateProjectDetails(this.recent_3_projects[0], 1);
    }
  }

  navigateTo(direction: number): void {
    this.showSlides(this.slideIndex + direction);
  }

  selectProject(index: number): void {
    this.showSlides(index + 1);
  }

  private showSlides(n: number): void {
    const totalProjects = this.recent_3_projects.length;
    this.slideIndex = n > totalProjects ? 1 : n < 1 ? totalProjects : n;
    const currentProject = this.recent_3_projects[this.slideIndex - 1];
    this.updateProjectDetails(currentProject, this.slideIndex);
  }

  private updateProjectDetails(project: any, slideIndex: number): void {
    this.recent_3_projects_name = project.name;
    this.recent_3_projects_date = project.date[0];
    this.project_number = `0${slideIndex}`;
    this.workflow_status = project.status[0];

    localStorage.setItem('date', project.date[0]);
    localStorage.setItem('project_id', project.id);
    localStorage.setItem('project_name', project.name);
    localStorage.setItem('tab_name', project.category);
  }

  get currentStepIndex(): number {
    const stepperIndexMap: { [key: string]: number } = {
      created: 0,
      ftp: 1,
      processing: 2,
      completed: 3,
    };
    return stepperIndexMap[this.workflow_status];
  }

  calculateProgressWidth(status: string): string {
    const statusMap: { [key: string]: string } = {
      created: '25%',
      ftp: '50%',
      processing: '75%',
      completed: '100%',
    };
    return statusMap[status] || '0%';
  }
}
