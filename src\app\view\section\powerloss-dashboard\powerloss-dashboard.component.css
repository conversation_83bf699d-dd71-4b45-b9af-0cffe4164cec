.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-md);
}
.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
.card-body {
  padding: var(--spacing-sm) var(--spacing-lg);
}
.card-title {
  margin: 0;
  text-align: center;
  color: var(--primary);
  font-weight: bold;
  font-size: var(--font-lg);
}
.col-md-12,
.col-md-6,
.col-md-4,
.col-md-3 {
  padding: 0 var(--spacing-sm);
}
.card-heading {
  text-align: center;
  font-size: var(--font-3xl);
  font-weight: 800;
  padding: var(--spacing-xl);
  line-height: 4rem;
}
@media (max-width: 767px) {
  .card {
    margin-bottom: var(--spacing-sm);
  }
  .card-heading {
    font-size: var(--font-xl);
    padding: var(--spacing-md);
    line-height: 2.5rem;
  }
  .card-body {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  .card-title {
    font-size: var(--font-md);
  }
  .col-md-12,
  .col-md-6,
  .col-md-4,
  .col-md-3 {
    padding: 0 var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
  }
  .row > [class*="col-"] {
    margin-bottom: var(--spacing-sm);
  }
}
