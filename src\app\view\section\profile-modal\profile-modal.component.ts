import { ApiConfigService } from '@/controller/api-config.service';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-profile-modal',
  templateUrl: './profile-modal.component.html',
  styleUrls: ['./profile-modal.component.css'],
})
export class ProfileModalComponent {
  Isworking = false;
  old_role: string = null;

  constructor(
    private apiConfigService: ApiConfigService,
    private toastr: ToastrService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<ProfileModalComponent>
  ) {
    this.old_role = this.data.user.role;
  }

  onUpdate() {
    if (this.data.user.role != this.old_role) {
      this.Isworking = true;

      this.apiConfigService.roleAccess({ ...this.data.user, old_role: this.old_role }).subscribe(
        (res: any) => {
          if (res.status == 'success') {
            this.Isworking = false;
            this.toastr.success(res.message);
            this.dialogRef.close({ dataModified: true });
          } else {
            this.Isworking = false;
            this.toastr.error(res.message);
          }
        },
        err => {
          this.Isworking = false;
          this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
          console.error(err);
        }
      );
    } else {
      this.toastr.warning('No change detected', 'Role already assigned');
    }
  }
  onClose() {
    this.dialogRef.close({ dataModified: false });
  }
}
