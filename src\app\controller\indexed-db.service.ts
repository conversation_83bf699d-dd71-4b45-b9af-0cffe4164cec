import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DataService } from './data.service';

@Injectable({
  providedIn: 'root',
})
export class IndexedDbService {
  private readonly DB_NAME = 'carnot-datasee-ai';
  private readonly STORE_NAME = 'store';
  private readonly SESSION_TIMEOUT = 2 * 3600000; // 2 hours in milliseconds
  private db: IDBDatabase;
  private dbInitialized: boolean = false;

  constructor(
    private dataService: DataService,
    private toastr: ToastrService
  ) {}

  async initializeDb(): Promise<void> {
    if (this.dbInitialized) {
      return;
    }
    return new Promise<void>((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, 1);
      request.onupgradeneeded = (event: IDBVersionChangeEvent) => {
        const db = (event.target as IDBOpenDBRequest).result;
        db.createObjectStore(this.STORE_NAME, { keyPath: 'key' });
      };
      request.onsuccess = (event: Event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
        this.dbInitialized = true;
        this.dataService.generateKey();
        resolve();
      };
      request.onerror = (event: Event) => {
        console.error('IndexedDB initialization error:', event);
        reject(event);
      };
      request.onblocked = () => {
        console.error('Database initialization blocked.');
        reject(new Error('Database initialization blocked'));
      };
    });
  }

  async getItem(key: string): Promise<any> {
    await this.initializeDb();
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.STORE_NAME], 'readonly');
      const objectStore = transaction.objectStore(this.STORE_NAME);
      const request = objectStore.get(key);
      request.onsuccess = async () => {
        try {
          if (request.result) {
            const storageItem = request.result;
            if (Date.now() - storageItem.timestamp > this.SESSION_TIMEOUT) {
              await this.destroyDb();
              localStorage.clear();
              console.clear();
              this.toastr.error('Please login again', 'Session Expired!');
              resolve(null);
              return;
            }
            const decryptedData = this.dataService.decryptData(storageItem.data);
            resolve(decryptedData);
          } else {
            resolve(null);
          }
        } catch (error) {
          console.error('Error during data decryption:', error);
          reject(error);
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  async setItem(key: string, value: any): Promise<void> {
    await this.initializeDb();
    return new Promise((resolve, reject) => {
      try {
        const encryptedData = this.dataService.encryptData(value);
        const storageItem = { data: encryptedData, timestamp: Date.now() };
        const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
        const objectStore = transaction.objectStore(this.STORE_NAME);
        const request = objectStore.put({ key, ...storageItem });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      } catch (error) {
        console.error('Error during data encryption or storage:', error);
        reject(error);
      }
    });
  }

  async removeItem(key: string): Promise<void> {
    await this.initializeDb();
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
      const objectStore = transaction.objectStore(this.STORE_NAME);
      const request = objectStore.delete(key);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async destroyDb(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close();
      }
      const request = indexedDB.deleteDatabase(this.DB_NAME);
      request.onsuccess = () => {
        this.dbInitialized = false;
        resolve();
      };
      request.onerror = event => {
        console.error('Error deleting database:', event);
        reject(event);
      };
      request.onblocked = () => {
        console.error('Database deletion blocked. Please close other tabs or reload this page.');
        reject(new Error('Database deletion blocked'));
      };
    });
  }
}
