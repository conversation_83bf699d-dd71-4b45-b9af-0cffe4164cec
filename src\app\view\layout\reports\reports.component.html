<div class="data">
  <div
    *ngIf="fileObj && objectKeys(fileObj).length > 0; then thenBlock; else noAccessTemplate"></div>
  <ng-template #thenBlock>
    <p class="title">Report and File Downloads</p>
    <p class="title mb-4">{{ data.projectName }} ({{ data.currentDate }})</p>
    <div class="mb-3">
      <label for="input-1" class="form-label font-weight-bolder">Select Block Wise:</label>
      <select class="form-select" id="input-1" [(ngModel)]="block_type" (change)="checkData()">
        <option [value]="null" disabled selected>Please select an option</option>
        <option value="SUMMARY">SUMMARY</option>
        <option value="INVERTER">INVERTER</option>
      </select>
    </div>
    <ng-container *ngIf="block_type === 'SUMMARY' && file_type_arr">
      <div class="mb-3">
        <label for="input-2" class="form-label font-weight-bolder">Select File Type:</label>
        <select class="form-select" id="input-2" [(ngModel)]="file_type">
          <option [value]="null" disabled selected>Please select an option</option>
          <option *ngFor="let option of file_type_arr" [value]="option">{{ option }}</option>
        </select>
      </div>
    </ng-container>
    <ng-container *ngIf="block_type === 'INVERTER'">
      <div class="mb-3" *ngIf="inverter_type_arr">
        <label for="input-3" class="form-label font-weight-bolder">Select Inverter Type:</label>
        <select class="form-select" id="input-3" [(ngModel)]="inverter_type" (change)="checkData()">
          <option [value]="null" disabled selected>Please select an option</option>
          <option *ngFor="let option of inverter_type_arr" [value]="option">{{ option }}</option>
        </select>
      </div>
      <div class="mb-3" *ngIf="inverter_type && file_type_arr">
        <label for="input-2" class="form-label font-weight-bolder">Select File Type:</label>
        <select class="form-select" id="input-2" [(ngModel)]="file_type">
          <option [value]="null" disabled selected>Please select an option</option>
          <option *ngFor="let option of file_type_arr" [value]="option">{{ option }}</option>
        </select>
      </div>
    </ng-container>
  </ng-template>
  <ng-template #noAccessTemplate>
    <p class="title">Downloads Not Available</p>
  </ng-template>
  <mat-dialog-actions class="justify-content-evenly">
    <button mat-button class="w-50 text-uppercase" *ngIf="file_type" (click)="downloadFile()">
      Download
    </button>
    <button mat-button (click)="onClose()" class="w-25 text-uppercase">Close</button>
  </mat-dialog-actions>
</div>
