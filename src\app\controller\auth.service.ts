import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { KeycloakInitOptions } from 'keycloak-js';
import { Subscription, interval } from 'rxjs';
import { environment } from 'src/environments/environment';

export function initializeKeycloak(keycloak: KeycloakService) {
  return async () => {
    const initOptions: KeycloakInitOptions = {
      onLoad: 'check-sso',
      flow: 'hybrid',
      checkLoginIframe: false,
    };
    try {
      await keycloak.init({
        config: environment.keycloak,
        initOptions,
        enableBearerInterceptor: true,
      });
    } catch (error) {
      console.error('Error initializing Keycloak', error);
    }
  };
}

@Injectable({
  providedIn: 'root',
})
export class AuthService implements OnDestroy {
  private tokenRefreshInterval: Subscription | null = null;
  private readonly customTokenKey = 'token';
  private readonly refreshTimeKey = 'tokenRefreshTime';
  private readonly refreshInterval = 240 * 1000; // 4 minutes

  constructor(
    private keycloak: KeycloakService,
    private http: HttpClient
  ) {
    if (this.isLoggedIn()) this.restoreTokenRefresh();
  }

  loginWithEmail(data: any): Promise<boolean> {
    return this.http
      .post<any>(`${environment.carnot_url}accounts/login/`, data)
      .toPromise()
      .then(response => {
        if (response.token) this.setCustomToken(response.token);
        return !!response.token;
      })
      .catch(error => {
        console.error('Login failed:', error);
        return false;
      });
  }

  loginWithSocial(provider: string): Promise<void> {
    return this.keycloak.login({ idpHint: provider });
  }

  isLoggedIn(): boolean {
    return !!this.getCustomToken() || this.keycloak.isLoggedIn();
  }

  async getToken(): Promise<string> {
    const customToken = this.getCustomToken();
    return customToken ? customToken['access_token'] : this.keycloak.getToken();
  }

  private refreshKeycloakToken(): Promise<boolean> {
    return this.keycloak.updateToken(60);
  }

  private async refreshCustomToken(): Promise<boolean> {
    try {
      const customToken = this.getCustomToken();
      const response = await this.http
        .post<any>(`${environment.carnot_url}accounts/refresh_token/`, {
          refresh_token: customToken['refresh_token'],
        })
        .toPromise();
      if (response?.token) {
        this.setCustomToken(response.token);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to refresh custom token:', error);
      return false;
    }
  }

  startTokenRefresh(): void {
    if (this.tokenRefreshInterval) this.stopTokenRefresh();
    this.scheduleNextRefresh(this.refreshInterval);
    this.tokenRefreshInterval = interval(this.refreshInterval).subscribe(async () => {
      try {
        const refreshSuccess = this.getCustomToken()
          ? await this.refreshCustomToken()
          : await this.refreshKeycloakToken();
        if (refreshSuccess) {
          this.scheduleNextRefresh(this.refreshInterval);
        }
      } catch (error) {
        console.error('Failed to refresh token:', error);
      }
    });
  }

  private restoreTokenRefresh(): void {
    const storedRefreshTime = parseInt(localStorage.getItem(this.refreshTimeKey) || '0', 10);
    const now = Date.now();
    if (storedRefreshTime > now) {
      const remainingTime = storedRefreshTime - now;
      setTimeout(() => this.startTokenRefresh(), remainingTime);
    } else {
      this.startTokenRefresh();
    }
  }

  private scheduleNextRefresh(intervalMs: number): void {
    const nextRefreshTime = Date.now() + intervalMs;
    localStorage.setItem(this.refreshTimeKey, nextRefreshTime.toString());
  }

  private stopTokenRefresh(): void {
    if (this.tokenRefreshInterval) {
      this.tokenRefreshInterval.unsubscribe();
      this.tokenRefreshInterval = null;
    }
    localStorage.removeItem(this.refreshTimeKey);
  }

  private setCustomToken(token: string): void {
    localStorage.setItem(this.customTokenKey, JSON.stringify(token));
  }

  getCustomToken(): any {
    const token = localStorage.getItem(this.customTokenKey);
    return token ? JSON.parse(token) : null;
  }

  private clearCustomToken(): void {
    localStorage.removeItem(this.customTokenKey);
  }

  async logout(): Promise<void> {
    this.stopTokenRefresh();
    if (this.getCustomToken()) {
      this.clearCustomToken();
    } else {
      await this.keycloak.logout();
    }
  }

  ngOnDestroy(): void {
    this.stopTokenRefresh();
  }
}
