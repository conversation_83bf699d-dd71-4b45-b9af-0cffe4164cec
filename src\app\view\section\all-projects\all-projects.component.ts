import { ApiConfigService } from '@/controller/api-config.service';
import { AuthService } from '@/controller/auth.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { ReportsComponent } from '@/view/layout/reports/reports.component';
import { VideoModalComponent } from '@/view/layout/video-modal/video-modal.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import * as L from 'leaflet';
import { ShareComponent } from 'src/app/view/layout/share/share.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-all-projects',
  templateUrl: './all-projects.component.html',
  styleUrls: ['./all-projects.component.css'],
})
export class AllProjectsComponent implements OnInit {
  terrain_tile_layer: string = environment.terrain_tile_layer;
  no_project = environment.no_project;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  dataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = [];
  map: L.Map = null;
  main_data: any = null;
  Current_date_status: any;
  project_list: any = [];
  paginatedProjects: any[] = [];
  itemsPerPage = 20;
  currentPage = 0;
  all_projects: any = [];
  usersList: [] = [];
  currentIndex = 0;
  tabName = 'all_projects';
  project_keys: any;
  all_projects_carnot: any = null;
  defaultOption = 'All Projects';
  category_wise_color = {
    topography: 'red',
    grading: 'yellow',
    thermography: 'green',
    vegetation: 'blue',
    'due diligence': 'ltblue',
  };
  markers: any = [];
  selected_point = new L.LayerGroup();
  current_View = 'grid';
  tab_name2 = 'thermography';
  isModalOpen = false;
  isModalOpen2 = false;
  noDataFound: boolean = false;
  dropdownOptions = [
    { label: 'Option 0', value: 'All Projects' },
    { label: 'Option 1', value: 'Topography' },
    { label: 'Option 2', value: 'Thermography' },
    { label: 'Option 3', value: 'Grading' },
    { label: 'Option 4', value: 'Vegetation' },
    { label: 'Option 5', value: 'Due Diligence' },
  ];

  constructor(
    public dialog: MatDialog,
    private router: Router,
    private userService: UserService,
    private authService: AuthService,
    private sharedDataService: SharedDataService,
    private apiConfigService: ApiConfigService
  ) {}

  ngOnInit(): void {
    this.apiCall();
  }
  replaceCharacters(value: string): string {
    return value.toUpperCase().replace(/[_-]/g, ' ');
  }
  getBadgeClass(status: string): string {
    switch (status) {
      case 'completed':
        return 'badge-success';
      case 'ftp':
        return 'badge-warning';
      case 'processing':
        return 'badge-info';
      default:
        return 'badge-danger';
    }
  }
  ViewClick(tab: string) {
    this.current_View = tab;
  }
  openDropdownModal(): void {
    this.isModalOpen = true;
  }
  openDropdownModal2(): void {
    this.isModalOpen2 = true;
    document.getElementById('id01').style.display = 'block';
  }
  closeModal(event: Event) {
    if (event.target === event.currentTarget) {
      document.getElementById('id01').style.display = 'none';
    }
  }
  apiCall() {
    this.sharedDataService
      .fetchData(this.apiConfigService.getProjectByCategory(), 'get_project_by_category')
      .subscribe(data => {
        if (data) {
          this.main_data = data;
          this.all_projects_carnot = Object.values(this.main_data).flat();

          if (!this.all_projects_carnot.length) this.noDataFound = true;
          this.category_project(this.tabName);

          const role = this.userService.getRole();
          const group_id = this.userService.getGroupId();
          if (role === 'admin' && group_id) {
            this.sharedDataService
              .fetchData(this.apiConfigService.getManageUsers(group_id), 'manage_users')
              .subscribe(data => {
                if (data) {
                  this.usersList = data;
                }
              });
          }
        }
      });
  }
  updatePaginatedProjects() {
    const startIndex = this.currentPage * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedProjects = this.project_list.slice(startIndex, endIndex);
  }
  onPageChange(event: PageEvent) {
    this.itemsPerPage = event.pageSize;
    this.currentPage = event.pageIndex;
    this.updatePaginatedProjects();
  }
  genrate_table() {
    if (this.all_projects_carnot && this.all_projects_carnot.length) {
      const tableData = this.all_projects_carnot
        .filter(project => project.center)
        .map(project => ({
          id: project.id,
          name: project.name.toUpperCase().replace(/[_-]/g, ' '),
          location: `${project.city}, ${project.state}, ${project.country}`,
          category: project.category.toUpperCase(),
          date: project.current_date,
          status: project.current_date_status,
        }));
      if (tableData.length) {
        this.displayedColumns = Object.keys(tableData[0]).filter(column => column !== 'id');
      }
      setTimeout(() => {
        this.dataSource.data = tableData;
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }, 200);
    }
  }
  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }
  create_map() {
    const options = {
      preferCanvas: true,
      attributionControl: false,
      minZoom: 2,
      maxZoom: 23,
      zoomSnap: 0.5,
      maxBoundsViscosity: 1.0,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    };
    try {
      this.map = L.map('mapdashboard', options).setView([0, 0], 1);
    } catch (error) {
      console.error('Error initializing the Leaflet map:', error);
    }
    if (this.map) {
      L.tileLayer(this.terrain_tile_layer, { noWrap: true }).addTo(this.map);
      this.map_location_marker(this.all_projects);
    }
  }
  map_location_marker(data) {
    data.forEach(project => {
      if (project.center) {
        const [lat, long] = project.center.split(',');
        const mapIcon = L.icon({
          iconSize: [30, 30],
          iconUrl:
            'http://maps.google.com/mapfiles/ms/icons/' +
            this.category_wise_color[project.category] +
            '-dot.png',
        });
        if (lat && long) {
          const popupContent = `
        <html>
          <head><style>.leaflet-popup-content-wrapper {text-transform: capitalize;cursor: pointer;}</style></head>
            <body>
            <table class="project-details">
              <tr style="display: none;"><td><span style="color: darkcyan;font-weight: bold;">ID: </span></td><td class="project-id">${project.id}</td></tr>
              <tr><td><span style="color: darkcyan;font-weight: bold;">Name: </span></td><td class="project-name">${project.name.toUpperCase().replace(/[_-]/g, ' ')}</a></td></tr>
              <tr><td><span style="color: darkcyan;font-weight: bold;">Location: </span></td><td class="project-city">${project.city}, ${project.state}, ${project.country}</td></tr>
              <tr><td><span style="color: darkcyan;font-weight: bold;">Category: </span></td><td class="project-category">${project.category.toUpperCase()}</td></tr>
              <tr><td><span style="color: darkcyan;font-weight: bold;">Date: </span></td><td class="current-date">${project.current_date}</td></tr>
              <tr><td><span style="color: darkcyan;font-weight: bold;">Status: </span></td><td class="current-date-status">${project.current_date_status}</td></tr>
            </table>
          </body>
        </html>
      `;
          const marker = L.marker([parseFloat(lat), parseFloat(long)], {
            icon: mapIcon,
          }).bindPopup(popupContent);
          this.selected_point.addLayer(marker).addTo(this.map);
          this.markers.push(marker);

          marker.on('popupopen', e => {
            const popup = e.popup;
            if (popup) {
              const contentNode = popup._contentNode;
              if (contentNode) {
                contentNode.addEventListener('click', e => {
                  e.preventDefault();
                  const target = e.target as HTMLElement;
                  const tableRow = target.closest('td')?.parentElement?.parentElement;
                  if (tableRow) {
                    const getElementText = (element: Element | null): string => {
                      return element?.textContent?.trim() || '';
                    };
                    const id = getElementText(tableRow.querySelector('.project-id'));
                    const name = getElementText(tableRow.querySelector('.project-name'));
                    const category = getElementText(tableRow.querySelector('.project-category'));
                    const currentDate = getElementText(tableRow.querySelector('.current-date'));
                    this.selectedMapdate(currentDate, name, id, category);
                  }
                });
              }
            }
          });
        }
      }
    });
    if (this.markers.length === 0) {
      this.markers.push(L.latLngBounds([-90, -180], [90, 180]));
      this.map.fitBounds(this.markers[0]);
    } else {
      const bounds = L.latLngBounds(this.markers.map(marker => marker.getLatLng()));
      this.map.fitBounds(bounds);
    }
  }
  handleDropdownChange(selectedValue: string) {
    this.isModalOpen = false;
    this.category_project(selectedValue.replace(/\s+/g, '_').toLocaleLowerCase());
  }
  category_project(category) {
    if (category == 'all_projects') this.project_list = this.all_projects_carnot;
    else this.project_list = this.main_data[category];
    const projectCount = this.project_list.length;
    for (let index = 0; index < projectCount; index++) {
      const element = this.project_list[index];
      const temp_date = Object.keys(element.date_status);
      const temp_date_status = Object.values(element.date_status);
      const projectData = {
        date: temp_date,
        current_date: temp_date[0],
        status: temp_date_status,
        current_date_status: temp_date_status[0],
      };
      this.project_list[index] = { ...element, ...projectData };
    }
    this.all_projects = this.project_list;
    this.updatePaginatedProjects();
  }
  onSearchChange(event) {
    const searchText = event.target.value.trim();
    if (searchText) {
      const searchTerm = searchText.toLowerCase();
      this.project_list = this.all_projects.filter(project =>
        project.name.toLowerCase().includes(searchTerm)
      );
    } else {
      this.project_list = this.all_projects;
    }
    this.updatePaginatedProjects();
  }
  selectedMapdate(date, name, id, category) {
    localStorage.setItem('project_name', name);
    localStorage.setItem('project_id', id);
    localStorage.setItem('date', date);
    localStorage.setItem('tab_name', category);

    this.router.navigate(['/map']);
  }
  selectedUploadPage(project_id) {
    const projectData = {
      project_id,
      portal: 'carnot',
    };
    const encodedData = encodeURIComponent(btoa(JSON.stringify(projectData)));
    const token = this.authService.getCustomToken();
    let url = `${environment.ftp_ui}?data=${encodedData}`;
    if (token) {
      const tokenData = encodeURIComponent(btoa(JSON.stringify(token)));
      url += `&customToken=${tokenData}`;
    }
    window.open(url, '_blank');
  }
  selectChange(date: string, proj_count_id: any, status: any) {
    this.project_list[proj_count_id].current_date_status = status;
    this.project_list[proj_count_id].current_date = date;
  }
  openReportdialog(name, date, processed_data) {
    const processed_data_date = processed_data[date];
    if (name && date && processed_data_date)
      this.dialog.open(ReportsComponent, {
        data: {
          projectName: name,
          currentDate: date,
          fileObj: processed_data_date.report_path,
        },
        disableClose: true,
      });
  }
  openSharedialog(project_name: any, id: any, users: []) {
    const dialogRef = this.dialog.open(ShareComponent, {
      data: {
        projectName: project_name,
        projectId: id,
        usersWithAccess: users,
        allUsers: this.usersList,
      },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(() => {
      this.apiCall();
    });
  }
  openVideodialog(url) {
    this.dialog.open(VideoModalComponent, {
      data: url,
      disableClose: true,
    });
  }
}
