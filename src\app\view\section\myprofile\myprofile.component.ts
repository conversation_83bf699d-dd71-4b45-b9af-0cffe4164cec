import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { Users } from '@/interfaces/users';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { City, Country, State } from 'country-state-city';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-myprofile',
  templateUrl: './myprofile.component.html',
  styleUrls: ['./myprofile.component.css'],
})
export class MyprofileComponent implements OnInit {
  profileForm: FormGroup;
  resetForm: FormGroup;
  users: Users = null;
  displayedColumns: string[] = ['ipAddress', 'lastAccess'];
  dataSource = new MatTableDataSource<any>([]);
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  Isworking = false;

  countries: any = [];
  states: any = [];
  cities: any = [];
  states_data: any = [];
  cities_data: any = [];
  countryCode: any;

  isValidPassword = true;
  hide2 = true;
  hide3 = true;

  constructor(
    private sharedDataService: SharedDataService,
    private apiConfigService: ApiConfigService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.countries = Country.getAllCountries();
    this.states = State.getAllStates();
    this.cities = City.getAllCities();
    this.sharedDataService
      .fetchData(this.apiConfigService.getUserDetails(), 'user_data')
      .subscribe(res => {
        if (res) {
          this.users = res;
          setTimeout(() => {
            this.dataSource.data = this.users.sessions;
            this.dataSource.paginator = this.paginator;
            this.dataSource.sort = this.sort;
          }, 200);
          this.createForm();
        }
      });
  }

  createForm() {
    this.profileForm = new FormGroup({
      username: new FormControl(
        { value: this.users.username, disabled: true },
        Validators.required
      ),
      first_name: new FormControl(this.users.first_name, Validators.required),
      last_name: new FormControl(this.users.last_name, Validators.required),
      contact: new FormControl(this.users.contact, Validators.required),
      email: new FormControl({ value: this.users.email, disabled: true }, Validators.required),
      country: new FormControl(this.users.country, Validators.required),
      state: new FormControl(this.users.state, Validators.required),
      city: new FormControl(this.users.city, Validators.required),
      pincode: new FormControl(this.users.pincode, Validators.required),
      role: new FormControl({ value: this.users.role, disabled: true }, Validators.required),
      company: new FormControl(
        { value: this.users.company ? this.users.company.name : '', disabled: true },
        Validators.required
      ),
    });
    this.resetForm = new FormGroup({
      password: new FormControl('', Validators.required),
      confirm_password: new FormControl('', Validators.required),
    });
  }
  hasError = (controlName: string, errorName: string) => {
    return this.profileForm?.controls[controlName].hasError(errorName);
  };
  hasError2 = (controlName: string, errorName: string) => {
    return this.resetForm?.controls[controlName].hasError(errorName);
  };
  selectCountry(countryCode: string) {
    this.countryCode = countryCode;
    this.users.country = null;
    this.users.state = null;
    this.users.city = null;
    this.profileForm.get('state').setValue('');
    this.profileForm.get('city').setValue('');
    this.states_data = this.states.filter(state => state.countryCode === countryCode);
  }
  selectState(stateCode: string) {
    this.cities_data = this.cities.filter(
      city => city.stateCode === stateCode && city.countryCode === this.countryCode
    );
  }
  updateDetails() {
    this.Isworking = true;

    const StateCode = this.profileForm.get('state').value;
    const CountryCode = this.profileForm.get('country').value;

    if (this.users.state != StateCode) {
      const state = State.getStateByCodeAndCountry(StateCode, CountryCode).name;
      this.profileForm.get('state').setValue(state);
    }
    if (this.users.country != CountryCode) {
      const country = Country.getCountryByCode(CountryCode).name;
      this.profileForm.get('country').setValue(country);
    }
    const data = {
      id: this.users.id,
      ...this.profileForm.getRawValue(),
    };
    this.apiConfigService.updateUserDetails(data).subscribe(
      (res: any) => {
        if (res['status'] == 'success') {
          this.Isworking = false;
          this.toastr.success(res.message);
          this.sharedDataService.remove('user_data');
          this.ngOnInit();
        }
      },
      err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        this.Isworking = false;
        console.error(err);
      }
    );
  }
  checkPassword() {
    if (this.resetForm.value.password === this.resetForm.value.confirm_password) {
      this.isValidPassword = true;
    } else {
      this.isValidPassword = false;
    }
  }
  resetPassword() {
    if (this.resetForm.valid && this.isValidPassword) {
      this.Isworking = true;
      const data = {
        password: this.resetForm.value.password,
        type: 'update',
        id: this.users.id,
      };
      this.apiConfigService.resetPassword(data).subscribe(
        (res: any) => {
          if (res['status'] == 'success') {
            this.toastr.success(res.message);
            this.Isworking = false;
          }
        },
        err => {
          this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
          this.Isworking = false;
          console.error(err);
        }
      );
    }
  }
}
