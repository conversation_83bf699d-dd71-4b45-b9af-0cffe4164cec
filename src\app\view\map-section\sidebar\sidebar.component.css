.compact-sidebar {
  height: 100%;
  width: 70px;
  position: fixed;
  z-index: 1000;
  background-color: var(--primary);
  border-right: 3px solid white;
  transition: transform 0.3s ease;
}
.compact-sidebar .menu {
  height: 50px;
  color: #eee;
  position: relative;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}
.sidebar-menu {
  padding: 5px;
}
.menu {
  display: flex;
  align-items: center;
}
.menu:not(:nth-last-child(0)) {
  border-top: 1px solid gray;
  margin-right: 10px;
  margin-left: 10px;
}
.compact-sidebar .menu .menu-icon {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 20px;
  color: #eee;
}
.compact-sidebar .menu .fa {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 20px;
  color: #eee;
}
.menu .active {
  color: var(--primary);
}
.bottom-part {
  bottom: 0;
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  margin-left: 10px;
  margin-right: 10px;
}
.compact-sidebar .menu .menu-active {
  width: 0;
  height: 75%;
  background-color: var(--primary);
  border-radius: 25%;
  position: absolute;
  right: -5px;
  top: 0;
  bottom: 0;
  margin: auto;
}
.compact-sidebar .sidebar-menu .top-section {
  position: relative;
}
.compact-sidebar .sidebar-menu .bottom-section {
  position: fixed;
}
thead tr th {
  background-color: var(--dark);
  color: var(--white);
  font-weight: 600;
  padding: 2px;
  text-align: center;
  vertical-align: middle;
}
tbody tr td {
  font-weight: 600;
  padding: 2px;
  text-align: center;
  vertical-align: middle;
}
.sidebar-card {
  background-color: var(--white);
  box-shadow: 0 3px 6px var(--shadow);
  left: 70px !important;
  height: 100%;
  position: absolute;
  padding: 10px;
  overflow: hidden;
  transition: 0.3s;
  overflow-y: scroll;
}
@media (max-width: 767px) {
  .sidebar-card {
    overflow-y: scroll;
  }
}
.sidebar-card .header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  text-transform: uppercase;
  font-size: 20px;
  font-weight: 600;
  padding: 0 !important;
}
.sidebar-card .icon-section {
  display: flex;
  position: relative;
  padding: 10px;
  scrollbar-width: none;
  overflow-x: auto;
}
.sidebar-card .icon-section .icon-container {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  text-align: center;
  height: auto;
  padding: 5px !important;
  cursor: pointer;
  gap: 0.5px;
}
@media (max-width: 767px) {
  .sidebar-card .icon-section .icon-container {
    margin-left: 9px;
  }
}
.icon-container .icon-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-sizing: border-box;
  position: relative;
  outline: none;
  border: none;
  text-align: center;
}
.icon-container .icon-name {
  font-size: medium;
  font-weight: 500;
  margin-top: 4px;
  line-height: normal;
}
.icon-btn-cont {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.icon-display {
  display: inline-flex;
}
.sidebar-card .details-section {
  margin: 10px 0;
}
.sidebar-card .des-card {
  padding: 10px;
  box-shadow: 0 3px 6px var(--shadow);
  border-radius: 5px;
  background-color: var(--primary);
  color: #eee;
}
.details .key {
  font-weight: 600;
  letter-spacing: 1px;
}
.details .count {
  font-weight: 600;
  font-size: 25px;
}
.section-card {
  padding: 20px;
  margin: 10px 0;
  box-shadow: 0 3px 6px var(--shadow);
  border-radius: 5px;
}
.table-card {
  margin: 10px 0;
  box-shadow: 0 3px 6px var(--shadow);
  border-radius: 5px;
}
.slider-wrapper {
  position: relative;
}
.slider-wrapper .slide-button {
  position: absolute;
  top: 50%;
  outline: none;
  border: none;
  height: 50px;
  width: 50px;
  z-index: 5;
  display: flex;
  cursor: pointer;
  font-size: 2.2rem;
  background: rgba(255, 255, 255, 0.2);
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transform: translateY(-50%);
}
.slider-wrapper .slide-button:hover {
  background: rgba(255, 255, 255, 0.5);
}
.slider-wrapper .slide-button#prev-slide {
  left: -25px;
}
.slider-wrapper .slide-button#next-slide {
  right: -25px;
}
.slide-button .btn-style {
  color: var(--primary);
}
.graph-class {
  justify-content: center;
  align-items: center;
}
.card {
  box-shadow: 0 0 8px #b5b5b5;
  border-radius: 5px;
}
.card-body {
  padding: 0.5rem;
}
.card-text {
  font-size: 0.7rem;
}
.card-icon {
  cursor: pointer;
  color: var(--primary);
}
.selected {
  border-color: var(--primary);
  border-width: 3px;
}
