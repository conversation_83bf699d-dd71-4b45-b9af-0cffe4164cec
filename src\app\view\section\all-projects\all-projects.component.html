<div *ngIf="all_projects_carnot; then thenBlock; else noAccessTemplate"></div>
<ng-template #noAccessTemplate>
  <div class="no-access-container" *ngIf="noDataFound">
    <img src="{{ no_project }}" alt="Not Found" class="no-access-image" />
    <button
      mat-button
      class="btn stepper-action-btn mt-3"
      *appHasRole="'admin'"
      [routerLink]="['/app', 'create-project']">
      Create Project
    </button>
  </div>
</ng-template>
<ng-template #thenBlock>
  <ng-container *appHasRole="['admin', 'manager', 'pilot']; else noAccessTemplate">
    <ng-container *ngIf="current_View == 'list'">
      <div class="search-box-top">
        <mat-form-field class="w-100">
          <mat-label class="field-content">Search..</mat-label>
          <mat-icon matPrefix class="field-icon">search</mat-icon>
          <input matInput placeholder="Search.." name="search" (input)="applyFilter($event)" />
        </mat-form-field>
      </div>
    </ng-container>
    <ng-container *ngIf="current_View == 'grid'">
      <div class="search-box-top">
        <mat-form-field class="w-100">
          <mat-label class="field-content">Search..</mat-label>
          <mat-icon matPrefix class="field-icon">search</mat-icon>
          <input matInput placeholder="Search.." name="search" (input)="onSearchChange($event)" />
        </mat-form-field>
        <div class="filter2-box-icon">
          <mat-form-field>
            <mat-select
              [(value)]="defaultOption"
              (selectionChange)="handleDropdownChange($event.value)">
              <mat-option *ngFor="let option of dropdownOptions" [value]="option.value">
                {{ option.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="modal-class2" *ngIf="isModalOpen2" (click)="closeModal($event)" id="id01">
        <input
          class="search-box-top2"
          placeholder="Enter project name..."
          (input)="onSearchChange($event)" />
        <mat-icon class="search-box-icon2">search</mat-icon>
      </div>
      <div class="modal-class" *ngIf="isModalOpen">
        <mat-form-field class="filter2-box-icon-mobile">
          <mat-select
            [(value)]="defaultOption"
            (selectionChange)="handleDropdownChange($event.value)">
            <mat-option
              *ngFor="let option of dropdownOptions"
              [value]="option.value"
              (click)="handleDropdownChange(option.value)">
              {{ option.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </ng-container>
    <div class="button-view">
      <button mat-mini-fab matTooltip="Grid View" (click)="ViewClick('grid')">
        <mat-icon>apps</mat-icon>
      </button>
      <button mat-mini-fab matTooltip="Map View" (click)="ViewClick('map'); create_map()">
        <mat-icon>map</mat-icon>
      </button>
      <button
        mat-mini-fab
        matTooltip="List View"
        (click)="ViewClick('list'); genrate_table()"
        id="isVisibleMobile">
        <mat-icon>list</mat-icon>
      </button>
      <button
        mat-mini-fab
        matTooltip="Filter Project"
        class="grid-view4"
        (click)="openDropdownModal()">
        <mat-icon>filter_list</mat-icon>
      </button>
      <button mat-mini-fab matTooltip="List View" class="grid-view5" (click)="openDropdownModal2()">
        <mat-icon>search</mat-icon>
      </button>
    </div>
    <ng-container *ngIf="current_View == 'grid'">
      <div *ngIf="project_list.length; else elseProjectBlock">
        <div class="row p-2">
          <div class="col-3 p-1 mb-2" *ngFor="let i of paginatedProjects; let k = index">
            <div class="project-container mat-elevation-z8">
              <div class="image-card">
                <img src="{{ i.image }}" class="project-img" />
                <div class="btn-icons-container">
                  <button
                    *ngIf="i.video"
                    class="mr-2"
                    mat-mini-fab
                    matTooltip="Current Field Status"
                    (click)="openVideodialog(i.video)">
                    <mat-icon>video_call</mat-icon>
                  </button>
                  <button
                    class="mr-2"
                    *appHasRole="'admin'"
                    mat-mini-fab
                    matTooltip="Share"
                    (click)="openSharedialog(i.name, i.id, i.users)">
                    <mat-icon>share</mat-icon>
                  </button>
                  <button
                    class="mr-2"
                    *appHasRole="['admin', 'manager']"
                    mat-mini-fab
                    matTooltip="Download"
                    (click)="openReportdialog(i.name, i.current_date, i.processed_data)">
                    <mat-icon>download</mat-icon>
                  </button>
                  <button mat-mini-fab matTooltip="Upload" (click)="selectedUploadPage(i.id)">
                    <mat-icon>cloud_upload</mat-icon>
                  </button>
                </div>
              </div>
              <div class="details-card" id="{{ i.name }}">
                <div
                  class="project-title trimmer"
                  [class.clickable]="i.current_date"
                  (click)="
                    i.current_date_status == 'completed' &&
                      selectedMapdate(i.current_date, i.name, i.id, i.category)
                  ">
                  {{ replaceCharacters(i.name) }}
                </div>
                <div class="project-capacity">
                  <p>{{ i.plant_capacity }}</p>
                  <p class="text-uppercase">{{ i.category }}</p>
                </div>
                <div class="project-category trimmer">
                  {{ i.city }}, {{ i.state }}, {{ i.country }}
                </div>
                <div
                  class="project-date"
                  [ngClass]="{ 'justify-content-end': !i.current_date_status }">
                  <div class="d-flex align-items-center" *ngIf="i.current_date">
                    <span id="date_{{ i.current_date }}">{{ i.current_date }}</span>
                    <mat-icon mat-button [matMenuTriggerFor]="menu">arrow_drop_down</mat-icon>
                    <mat-menu #menu="matMenu">
                      <button
                        mat-menu-item
                        (click)="selectChange(date, k, i.status[j])"
                        id="date_{{ k }}"
                        *ngFor="let date of i.date; let j = index">
                        {{ date }}
                      </button>
                    </mat-menu>
                  </div>
                  <span
                    class="badge badge-pill text-uppercase"
                    style="font-size: 80%"
                    [ngClass]="getBadgeClass(i.current_date_status)">
                    {{ i.current_date_status ? i.current_date_status : 'Created' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <mat-paginator
            [pageSize]="itemsPerPage"
            [length]="project_list.length"
            [pageSizeOptions]="[20, 40, 60]"
            (page)="onPageChange($event)"></mat-paginator>
        </div>
      </div>
      <ng-template #elseProjectBlock>
        <div class="text-center font-weight-bolder fs-2" style="margin-top: 100px !important">
          No Project Found..
        </div>
      </ng-template>
    </ng-container>
    <ng-container *ngIf="current_View == 'map'">
      <div id="mapdashboard"></div>
    </ng-container>
    <ng-container *ngIf="current_View == 'list'">
      <div class="mat-elevation-z8">
        <table mat-table [dataSource]="dataSource" matSort>
          <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ column | titlecase }}</th>
            <td mat-cell *matCellDef="let element">{{ element[column] }}</td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr
            mat-row
            (click)="selectedMapdate(row.date, row.name, row.id, row.category)"
            *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator [pageSizeOptions]="[10, 20, 30]" showFirstLastButtons></mat-paginator>
      </div>
    </ng-container>
  </ng-container>
</ng-template>
