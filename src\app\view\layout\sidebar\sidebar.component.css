.sidebar-base {
  display: flex;
  flex-direction: column;
}
.sidebar-base.sidebar-mobile {
  position: relative;
  background: var(--primary);
}
.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.1);
}
.logo-img {
  max-width: 85%;
  height: auto;
  max-height: 48px;
}
.fix-bottom {
  position: fixed;
  bottom: 0;
  width: 70px;
  list-style: none;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.menu-text,
.menu-text:hover,
a {
  text-decoration: none;
}
#compact-sidebar,
.fix-bottom,
.fix-top,
.list-item {
  display: flex;
}
.fix-bottom {
  position: fixed;
  bottom: 0;
}
.fix-top {
  position: relative;
  width: 70px;
  display: flex;
  flex-direction: column;
  height: 275px;
  justify-content: space-evenly;
  align-items: center;
}
.nav-item {
  width: 60px;
  height: fit-content;
  padding: 5px 0;
  margin: 2.5px 0;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}
.nav-item:hover {
  background-color: lightslategrey !important;
  cursor: pointer;
}
.fixed-top {
  position: relative;
}
.nav-active {
  border-left: 5px solid var(--primary);
  background-color: #0060fe1c;
  color: var(--primary) !important;
}
.nav-icon {
  font-size: 25px;
  color: var(--white);
}
.nav-icon:hover {
  font-size: 27px;
  font-weight: 400;
}
.logo {
  width: 12.5rem;
  height: 3.125rem;
  padding: 4px;
}
.nav-text {
  font-size: 10px;
  font-weight: 600;
  text-decoration: none;
  color: var(--white);
  text-align: center;
}
.nav-text:hover {
  text-decoration: none;
  cursor: pointer;
}
a {
  text-decoration: none;
}
.border-btn {
  border: 1px solid;
}
::ng-deep .mat-drawer-side {
  border-right: solid 0 rgba(0, 0, 0, 0.12) !important;
  box-shadow: 0 3px 3px var(--shadow) !important;
  background-color: var(--nav) !important;
}
::ng-deep .mat-nav-base .mat-subheader {
  color: var(--white);
}
#compact-sidebar {
  display: flex;
  transition: transform 0.3s ease;
}
@media (max-width: 767px) {
  #compact-sidebar {
    position: fixed;
    top: 56px;
    left: -70px;
    height: calc(100vh - 56px);
    z-index: var(--z-mobile-sidebar);
    background-color: var(--primary);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
    transition: left 0.3s ease;
    width: 70px;
  }
  #compact-sidebar.mobile-open {
    left: 0;
  }
  .sidebar-base.sidebar-mobile {
    position: relative;
    background: var(--primary);
    height: 100%;
    width: 100%;
  }
  .fix-top {
    height: auto;
    min-height: calc(100vh - 56px);
    justify-content: flex-start;
    padding-top: var(--spacing-md);
  }
  .nav-item {
    margin-bottom: var(--spacing-sm);
    width: 55px;
    cursor: pointer;
  }
  .nav-icon {
    font-size: 20px;
  }
  .nav-text {
    font-size: 9px;
    text-align: center;
    margin-top: 2px;
  }
  .close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
}
