import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, from, of, switchMap, tap, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { AuthService } from './auth.service';
import { IndexedDbService } from './indexed-db.service';
import { UserService } from './user.service';

@Injectable({
  providedIn: 'root',
})
export class SharedDataService {
  constructor(
    private router: Router,
    private http: HttpClient,
    private indexDB: IndexedDbService,
    private userService: UserService,
    private authService: AuthService
  ) {}

  fetchData(apiUrl: string, cacheKey: string): Observable<any> {
    return from(this.indexDB.getItem(cacheKey)).pipe(
      switchMap(data => {
        if (data) {
          return of(data);
        }
        return this.fetchDataFromApi(apiUrl, cacheKey);
      }),
      catchError(error => {
        console.error('Error reading from cache:', error);
        return this.fetchDataFromApi(apiUrl, cacheKey);
      })
    );
  }

  fetchDataFromApi(apiUrl: string, cacheKey: string): Observable<any> {
    return this.http.get(apiUrl).pipe(
      tap(urlData => {
        if (urlData && urlData['data']) {
          this.indexDB.setItem(cacheKey, urlData['data']);
        }
      }),
      map(urlData => urlData['data']),
      catchError(error => {
        console.error(`Error fetching data from ${apiUrl}:`, error);
        return throwError(() => new Error('Failed to fetch data from API.'));
      })
    );
  }

  remove(cacheKey: string) {
    this.indexDB.removeItem(cacheKey);
  }

  async clearData(): Promise<void> {
    this.authService.logout();
    this.userService.resetUserData();
    await this.indexDB.destroyDb();
    this.router.navigate(['/auth', 'login']);
    localStorage.clear();
    console.clear();
  }
}
