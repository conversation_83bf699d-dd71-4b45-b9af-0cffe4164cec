.label-text {
  font-weight: 600;
  font-size: 14px;
  text-align: left;
  text-transform: uppercase;
}
.logo-cont {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  width: 80vh;
  height: 70vh;
}
@media (max-width: 767px) {
  .inner_login {
    height: 100vh;
    flex-direction: column;
    padding: var(--spacing-md);
  }
  .logo-cont {
    height: 50vh;
    width: 95%;
    max-width: 400px;
  }
  .login-cont {
    width: 95% !important;
    max-width: 400px !important;
    min-height: fit-content !important;
  }
  .info_heading {
    font-size: var(--font-lg);
    text-align: center;
  }
  .info_text {
    text-align: center;
  }
  .width-set {
    width: 100% !important;
    text-align: center;
  }
  .label-text {
    text-align: center;
  }
  .bottom-set {
    font-size: var(--font-sm);
    justify-content: center !important;
    text-align: center;
  }
  .mat-mdc-form-field {
    width: 100% !important;
    margin-bottom: var(--spacing-sm);
  }
  .login-with-btn {
    margin: var(--spacing-xs);
    min-width: 48px;
    min-height: 48px;
  }
}
.flex-col {
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    width: 20%;
  }
}
.info_text {
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  line-height: 1.8rem;
  text-transform: capitalize;
}
.info_heading {
  text-align: center;
  font-weight: 600;
  font-size: 24px;
  text-transform: uppercase;
}
.inner_login {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  background-color: rgb(248 250 252);
}
.login-cont {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  width: 80vh;
  padding: 1rem;
  box-shadow: 0 0 8px #b5b5b5;
  background: white;
}
.login-with-btn {
  transition:
    background-color 0.3s,
    box-shadow 0.3s;
  border-radius: 5px !important;
  box-shadow:
    -1px 0 0 rgba(0, 0, 0, 0.04),
    0 1px 1px rgba(0, 0, 0, 0.25) !important;
  color: var(--dark);
  background-color: rgb(248 250 252) !important;
  background-repeat: no-repeat;
  background-position: 8px;
  margin: 0.5rem;
  &:hover {
    box-shadow:
      0 -1px 0 rgba(0, 0, 0, 0.04),
      0 2px 4px rgba(0, 0, 0, 0.25);
  }
  &:active {
    background-color: var(--primaryBg);
  }
  &:focus {
    outline: none;
    box-shadow:
      0 -1px 0 rgba(0, 0, 0, 0.04),
      0 2px 4px rgba(0, 0, 0, 0.25),
      0 0 0 3px #c8dafc;
  }
}
.google {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjUiIGhlaWdodD0iMjUiIHZpZXdCb3g9IjAgMCA0OCA0OCI+CjxwYXRoIGZpbGw9IiNGRkMxMDciIGQ9Ik00My42MTEsMjAuMDgzSDQyVjIwSDI0djhoMTEuMzAzYy0xLjY0OSw0LjY1Ny02LjA4LDgtMTEuMzAzLDhjLTYuNjI3LDAtMTItNS4zNzMtMTItMTJjMC02LjYyNyw1LjM3My0xMiwxMi0xMmMzLjA1OSwwLDUuODQyLDEuMTU0LDcuOTYxLDMuMDM5bDUuNjU3LTUuNjU3QzM0LjA0Niw2LjA1MywyOS4yNjgsNCwyNCw0QzEyLjk1NSw0LDQsMTIuOTU1LDQsMjRjMCwxMS4wNDUsOC45NTUsMjAsMjAsMjBjMTEuMDQ1LDAsMjAtOC45NTUsMjAtMjBDNDQsMjIuNjU5LDQzLjg2MiwyMS4zNSw0My42MTEsMjAuMDgzeiI+PC9wYXRoPjxwYXRoIGZpbGw9IiNGRjNEMDAiIGQ9Ik02LjMwNiwxNC42OTFsNi41NzEsNC44MTlDMTQuNjU1LDE1LjEwOCwxOC45NjEsMTIsMjQsMTJjMy4wNTksMCw1Ljg0MiwxLjE1NCw3Ljk2MSwzLjAzOWw1LjY1Ny01LjY1N0MzNC4wNDYsNi4wNTMsMjkuMjY4LDQsMjQsNEMxNi4zMTgsNCw5LjY1Niw4LjMzNyw2LjMwNiwxNC42OTF6Ij48L3BhdGg+PHBhdGggZmlsbD0iIzRDQUY1MCIgZD0iTTI0LDQ0YzUuMTY2LDAsOS44Ni0xLjk3NywxMy40MDktNS4xOTJsLTYuMTktNS4yMzhDMjkuMjExLDM1LjA5MSwyNi43MTUsMzYsMjQsMzZjLTUuMjAyLDAtOS42MTktMy4zMTctMTEuMjgzLTcuOTQ2bC02LjUyMiw1LjAyNUM5LjUwNSwzOS41NTYsMTYuMjI3LDQ0LDI0LDQ0eiI+PC9wYXRoPjxwYXRoIGZpbGw9IiMxOTc2RDIiIGQ9Ik00My42MTEsMjAuMDgzSDQyVjIwSDI0djhoMTEuMzAzYy0wLjc5MiwyLjIzNy0yLjIzMSw0LjE2Ni00LjA4Nyw1LjU3MWMwLjAwMS0wLjAwMSwwLjAwMi0wLjAwMSwwLjAwMy0wLjAwMmw2LjE5LDUuMjM4QzM2Ljk3MSwzOS4yMDUsNDQsMzQsNDQsMjRDNDQsMjIuNjU5LDQzLjg2MiwyMS4zNSw0My42MTEsMjAuMDgzeiI+PC9wYXRoPgo8L3N2Zz4=);
}
.microsoft {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjUiIGhlaWdodD0iMjUiIHZpZXdCb3g9IjAgMCA0OCA0OCI+CjxwYXRoIGZpbGw9IiNmZjU3MjIiIGQ9Ik02IDZIMjJWMjJINnoiIHRyYW5zZm9ybT0icm90YXRlKC0xODAgMTQgMTQpIj48L3BhdGg+PHBhdGggZmlsbD0iIzRjYWY1MCIgZD0iTTI2IDZINDJWMjJIMjZ6IiB0cmFuc2Zvcm09InJvdGF0ZSgtMTgwIDM0IDE0KSI+PC9wYXRoPjxwYXRoIGZpbGw9IiNmZmMxMDciIGQ9Ik0yNiAyNkg0MlY0MkgyNnoiIHRyYW5zZm9ybT0icm90YXRlKC0xODAgMzQgMzQpIj48L3BhdGg+PHBhdGggZmlsbD0iIzAzYTlmNCIgZD0iTTYgMjZIMjJWNDJINnoiIHRyYW5zZm9ybT0icm90YXRlKC0xODAgMTQgMzQpIj48L3BhdGg+Cjwvc3ZnPg==);
}
.email {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAACXBIWXMAAAsTAAALEwEAmpwYAAACUklEQVR4nOVUS4iNYRh+aIiQS0nIQiliIVmwoFw22LiVS41bsmDlUkrkTzmd8z3v9805OLOYhVmwtGFkFBuZpJiFWKDUZEFRFIXJTI7+73/P5ytzLqZjIW/99f/P/77P872X7wX+PysUpsLa9SDbIbIPxmyCc3NbQ27tcpA3QH6HSOW3h3wEcvPoyCuVMSDPgRxWwhcQyYM8BJE9IE9C5Hb4T3YjSdr+TIQsKflniOxAkowd0a9QWALyqQp1Ni8gskuDPsC5pQ398/npEBnwMdauaiyQJBMg8kZF9kd4G8jDIPtAPvelSjNMy5plvlUzv95MFgdVoD8QdHWNA9lTo/HHo0O8AznoD9pA5K4GH42wM0r63o9xlu09xY5Efre0ZMvqi5CfNHi+Bk4KGNke+T1RbHuEdWrsutoCpdIsDRwM0/RrCL4iScZHhG89bszqKJOiimyrLeLcSiV8GZFZDXwcsPQAIkMqsjDy7VbfjbVFRFaoyLMIu6pYT8ByuZmh8cXitEikz2POLagnsliDX0XYZRV5EDBrtyg2HCbQmDl6+wdQ1zo6Zofg6hiKnFDhIRiz0/dA5LX6/YC1i1AuTwbZq37H6otkpNWLuMF/OzcjNFnCk97u++qXLs5v+t7b3P4SKSvRzYClNSavKPFFP4Xp6icvgHwI8o6/L00vyIywutYP4K+ZyKnQG5GzcG7iiH7GTBm9SDoxIud9Y7OMPoK8BjIH8jTIS1q6L433VCNL13a2bYdqLMd+P5EtsbTJxqyFtbtB7oXIGlg7rzXk/5L9BE3tnlvke1aeAAAAAElFTkSuQmCC);
}
.feature-text {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  gap: 5px;
  font-weight: 500;
}
