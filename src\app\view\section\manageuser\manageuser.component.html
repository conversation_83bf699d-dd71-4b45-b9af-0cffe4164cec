<div class="container text-center">
  <p class="title">Manage Users</p>
  <div class="table-responsive">
    <table mat-table [dataSource]="dataSource" matSort>
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Full Name</th>
        <td mat-cell *matCellDef="let user" class="text-capitalize">
          {{ user.name }}
        </td>
      </ng-container>
      <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
        <td mat-cell *matCellDef="let user" class="text-lowercase">{{ user.email }}</td>
      </ng-container>
      <ng-container matColumnDef="role">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Role</th>
        <td mat-cell *matCellDef="let user" class="text-capitalize">
          {{ user.role ? user.role : '---' }}
        </td>
      </ng-container>
      <ng-container matColumnDef="active">
        <th mat-header-cell *matHeaderCellDef>Active</th>
        <td mat-cell *matCellDef="let user">
          <mat-slide-toggle
            [(ngModel)]="user.enabled"
            (change)="toggleUser(user)"></mat-slide-toggle>
        </td>
      </ng-container>
      <ng-container matColumnDef="created_on">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Created On</th>
        <td mat-cell *matCellDef="let user">
          {{ user.created_on | date: 'dd-MM-yyyy, HH:mm:ss a' }}
        </td>
      </ng-container>
      <ng-container matColumnDef="edit">
        <th mat-header-cell *matHeaderCellDef>Edit</th>
        <td mat-cell *matCellDef="let user">
          <button mat-mini-fab (click)="openDialog(user)" [disabled]="!user.enabled">
            <mat-icon>edit</mat-icon>
          </button>
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
  <mat-paginator
    [pageSize]="10"
    [pageSizeOptions]="[10, 20, 50]"
    showFirstLastButtons></mat-paginator>
</div>
