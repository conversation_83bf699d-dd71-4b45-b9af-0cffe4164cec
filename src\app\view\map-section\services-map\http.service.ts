import { ApiConfigService } from '@/controller/api-config.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, firstValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class HttpService {
  total_rectify_data: any[] = [];
  files: any[] = [];
  mission_val: any[] = [];
  set_mision_flight_data: any[] = [];
  close_side_bar = new BehaviorSubject<any>({ close_side_bar: 'summarySidebar' });
  visibility = new BehaviorSubject<any>({ visibility_val: 'visible' });
  is_analytics_icon = new BehaviorSubject<any>(false);

  constructor(
    private toastr: ToastrService,
    private apiConfigService: ApiConfigService,
    private http: HttpClient
  ) {}

  setclosesidebar(close_side_bar: any) {
    this.close_side_bar.next(close_side_bar);
  }
  getclosesidebar() {
    return this.close_side_bar.asObservable();
  }
  setsubdefects(visibility) {
    this.visibility.next(visibility);
  }
  getsubdefects() {
    return this.visibility.asObservable();
  }
  set_analytics_icon(data) {
    this.is_analytics_icon.next(data);
  }
  get_analytics_icon() {
    return this.is_analytics_icon;
  }
  async fileDownload(fileName, fileUrl: string) {
    if (!fileUrl) {
      this.toastr.warning('No file available for download.');
      return;
    }
    const signedUrl = await this.getSignedUrl(fileUrl);
    if (!signedUrl) {
      this.toastr.error('Failed to generate download link.');
      return;
    }
    this.toastr.info('Please wait..', 'Downloading Started!!!');

    const headers = new HttpHeaders().set('X-Skip-Authorization', 'true');
    this.http.get(signedUrl, { headers, responseType: 'blob' }).subscribe({
      next: blob => {
        const ext = signedUrl.split('.').pop()?.toLowerCase();
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = `${fileName}.${ext}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl);
      },
      error: () => {
        this.toastr.error('Download failed. Please try again.');
      },
    });
  }
  async getSignedUrl(fileUrl: string): Promise<string> {
    try {
      const urlParts = fileUrl.split('/');
      const bucketName = urlParts[2].split('.')[0];
      const fileKey = urlParts.slice(3).join('/');
      const res = await firstValueFrom(this.apiConfigService.getSignedUrl(bucketName, fileKey));
      if (res && res[0].url) {
        return res[0].url;
      } else {
        this.toastr.warning('No signed URL was generated');
        return null;
      }
    } catch (err) {
      console.error('Error getting signed URL:', err);
      this.toastr.error('Failed to generate download link');
      return null;
    }
  }
}
