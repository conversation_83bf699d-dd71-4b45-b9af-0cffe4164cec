import { ApiConfigService } from '@/controller/api-config.service';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-recovery',
  templateUrl: './recovery.component.html',
  styleUrls: ['./recovery.component.css'],
})
export class RecoveryComponent implements OnInit {
  loginForm: FormGroup;
  isValidPassword = true;
  Isworking = false;

  hide1 = true;
  hide2 = true;
  uid: string = null;
  token: string = null;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private apiConfigService: ApiConfigService,
    private toastr: ToastrService
  ) {
    this.loginForm = new FormGroup({
      password: new FormControl('', Validators.required),
      cpassword: new FormControl('', Validators.required),
    });
  }
  ngOnInit(): void {
    this.uid = this.route.snapshot.paramMap.get('uid');
    this.token = this.route.snapshot.paramMap.get('token');
  }
  hasError = (controlName: string, errorName: string) => {
    return this.loginForm.controls[controlName].hasError(errorName);
  };
  checkPassword() {
    if (this.loginForm.value.password === this.loginForm.value.cpassword) {
      this.isValidPassword = true;
    } else {
      this.isValidPassword = false;
    }
  }
  resetPassword() {
    if (!this.uid && !this.token) {
      this.toastr.error('Please Try Again or Contact Admin', 'Invalid Link');
      this.router.navigate(['/auth', 'login']);
    }
    if (this.loginForm.valid && this.isValidPassword) {
      this.Isworking = true;
      const data = {
        password: this.loginForm.value.password,
        type: 'reset',
        id: this.uid,
        token: this.token,
      };
      this.apiConfigService.resetPassword(data).subscribe(
        (res: any) => {
          if (res['status'] == 'success') {
            this.router.navigate(['/auth', 'login']);
            this.toastr.success(res.message);
            this.Isworking = false;
          }
        },
        err => {
          this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
          this.Isworking = false;
          console.error(err);
        }
      );
    }
  }
}
