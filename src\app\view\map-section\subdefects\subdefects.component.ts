import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import {
  Component,
  DoCheck,
  EventEmitter,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { HttpService } from '../services-map/http.service';

@Component({
  selector: 'subdefects',
  templateUrl: './subdefects.component.html',
  styleUrls: ['./subdefects.component.css'],
})
export class SubdefectsComponent implements OnInit, DoCheck {
  @ViewChild('imageDialog') imageDialog!: TemplateRef<any>;
  @Output() subdefects_page_event = new EventEmitter<[number, number]>();
  isVisible = true;
  paginatedDefects: any[] = [];
  itemsPerPage = 10;
  currentPage = 0;
  summ: any;
  date!: string;
  mode!: string;
  kml_file_location!: string;
  inventor_keys: string[] = [];
  allPlaceMarks: any[] = [];
  defects: any[] = [];
  polies: any[] = [];
  selectedTable: string = null;
  previousMode = '';
  previousDate = '';
  previousTab = '';
  previousSubDefect = '';
  previousPage = '';

  constructor(
    private _http: HttpService,
    private sharedDataService: SharedDataService,
    private apiConfigService: ApiConfigService,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.date = localStorage.getItem('date') || '';
    this.mode = localStorage.getItem('mode') || '';
    this.previousDate = this.date;
    this.previousMode = this.mode;
    this.previousTab = localStorage.getItem('current_tab') || '';
    this.previousSubDefect = localStorage.getItem('sub_defects') || '';
    this.previousPage = localStorage.getItem('page') || '';
    this.fetchProjectSummary();
  }
  ngDoCheck(): void {
    const currentDate = localStorage.getItem('date') || '';
    const currentMode = localStorage.getItem('mode') || '';
    const currentTab = localStorage.getItem('current_tab') || '';
    const currentSubDefect = localStorage.getItem('sub_defects') || '';
    const currentPage = localStorage.getItem('page') || '';

    const hasChanged =
      currentDate !== this.previousDate ||
      currentMode !== this.previousMode ||
      currentTab !== this.previousTab ||
      currentSubDefect !== this.previousSubDefect ||
      currentPage !== this.previousPage;

    if (hasChanged) {
      this.previousDate = currentDate;
      this.previousMode = currentMode;
      this.previousTab = currentTab;
      this.previousSubDefect = currentSubDefect;
      this.previousPage = currentPage;

      this.date = currentDate;
      this.mode = currentMode;
      this.allPlaceMarks = [];
      this.defects = [];
      this.paginatedDefects = [];
      this.fetchProjectSummary();
    }
  }
  private fetchProjectSummary() {
    const project_id = localStorage.getItem('project_id')!;
    this.sharedDataService
      .fetchData(this.apiConfigService.getProjectData(project_id), `project_id_${project_id}`)
      .subscribe(data => {
        this.summ = data;
        this.kml_file_location = data.processed_data[this.date].kml_file_location;
        this.inventor_keys = Object.keys(data.processed_data[this.date].inverter_layers);
        this.loadInitialDefects();
      });
  }
  private loadInitialDefects() {
    const tab = localStorage.getItem('current_tab')!;
    const pageIndex = Number(localStorage.getItem('page')!);
    const handlers: Record<string, () => void> = {
      summary: () =>
        this.loadDefects('GLOBAL/', this.summ.processed_data[this.date].summary_layers[tab].Count),
      summary_sub_details: () => {
        const sub = localStorage.getItem('sub_defects')!;
        const cnt = this.summ.processed_data[this.date].summary_layers[tab].sub_group[sub].Count;
        this.loadDefects('GLOBAL/', cnt);
      },
      inverter: () =>
        this.loadDefects(
          `INVERTER${pageIndex.toString().padStart(2, '0')}/`,
          this.summ.processed_data[this.date].inverter_layers[this.inventor_keys[pageIndex - 1]][
            tab
          ].count
        ),
      inverter_sub_details: () => {
        const sub = localStorage.getItem('sub_defects')!;
        const cnt =
          this.summ.processed_data[this.date].inverter_layers[this.inventor_keys[pageIndex - 1]][
            tab
          ].sub_group[sub].count;
        this.loadDefects(`INVERTER${pageIndex.toString().padStart(2, '0')}/`, cnt);
      },
    };
    const handler = handlers[this.mode];
    if (handler) handler();
  }
  private loadDefects(folder: string, count: number) {
    this.defects = [];
    if (count === 0) return;
    this.toggleSidebarZIndex('open');
    const filenames = localStorage.getItem('kmlfilename')!.split(',');
    this.loadKmlArray(folder, filenames).then(() => {
      this.buildDefectsFromPlaceMarks();
      this.updatePaginatedDefects();
    });
  }
  private async loadKmlArray(folder: string, filenames: string[]) {
    const parser = new DOMParser();
    const promises = filenames.map(async name => {
      try {
        const res = await fetch(this.kml_file_location + folder + name + '.kml');
        const xml = parser.parseFromString(await res.text(), 'text/xml');
        Array.from(xml.getElementsByTagName('Placemark')).forEach(pm => {
          const desc = pm.getElementsByTagName('description')[0]?.textContent || '';
          const rawCoords = pm.getElementsByTagName('coordinates')[0]?.textContent || '';
          const coords = rawCoords.replace(/\n/g, ' ').split(/[ ,]+/).filter(Boolean);
          this.allPlaceMarks.push({ coords, description: desc });
        });
      } catch (e) {
        console.error(`Failed to load ${name}.kml`, e);
      }
    });
    await Promise.all(promises);
  }
  private buildDefectsFromPlaceMarks() {
    this.defects = this.allPlaceMarks.map(pm => {
      const cells = Array.from(
        new DOMParser().parseFromString(pm.description, 'text/html').getElementsByTagName('td')
      );
      const obj: any = {};
      for (let i = 0; i + 1 < cells.length; i += 2) {
        const key = cells[i].textContent!.trim().replace(/:$/, '').replace(/\s+/g, '_');
        const valEl = cells[i + 1].querySelector('img') ?? cells[i + 1];
        obj[key] = (valEl as HTMLElement).getAttribute('src') ?? valEl.textContent!.trim();
      }
      return obj;
    });
  }
  private updatePaginatedDefects(): void {
    const start = this.currentPage * this.itemsPerPage;
    this.paginatedDefects = this.defects.slice(start, start + this.itemsPerPage);
  }
  onPageChange(e: PageEvent) {
    this.itemsPerPage = e.pageSize;
    this.currentPage = e.pageIndex;
    this.updatePaginatedDefects();
  }
  toggleSidebarZIndex(state: 'open' | 'close') {
    const el = document.getElementById('defectssidebar');
    if (!el) return;
    el.style.zIndex = state === 'open' ? (window.innerWidth < 800 ? '1100' : '1000') : '0';
  }
  toggleSidebarVisibility() {
    this.isVisible = false;
    setTimeout(() => (this.isVisible = true), 100);
  }
  selectedMaplocation(lat, long, table) {
    this.selectedTable = table;
    this.subdefects_page_event.emit([lat, long]);
  }
  openDialog(tmpl: TemplateRef<any>, defect: any) {
    this.dialog.open(tmpl, { data: defect, width: '80%', height: '80%' });
  }
  async downloadImage(src: string) {
    const name = src.split('/').pop()!;
    await this._http.fileDownload(name, src);
  }
}
