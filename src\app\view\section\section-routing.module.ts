import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PageNotFoundComponent } from '../page-not-found/page-not-found.component';
import { AllProjectsComponent } from './all-projects/all-projects.component';
import { AnalyticsComponent } from './analytics/analytics.component';
import { CreateProjectComponent } from './create-project/create-project.component';
import { DefectRectificationComponent } from './defect-rectification/defect-rectification.component';
import { ManageuserComponent } from './manageuser/manageuser.component';
import { MyprofileComponent } from './myprofile/myprofile.component';
import { PowerlossDashboardComponent } from './powerloss-dashboard/powerloss-dashboard.component';
import { SectionComponent } from './section.component';

const routes: Routes = [
  {
    path: '',
    component: SectionComponent,
    children: [
      { path: '', redirectTo: 'home', pathMatch: 'full' },
      {
        path: 'home',
        loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule),
      },
      { path: 'create-project', component: CreateProjectComponent },
      { path: 'all-projects', component: AllProjectsComponent },
      { path: 'my-profile', component: MyprofileComponent },
      { path: 'manage-users', component: ManageuserComponent },
      { path: 'analytics', component: AnalyticsComponent },
      { path: 'defect-recification', component: DefectRectificationComponent },
      { path: 'powerloss-dashboard', component: PowerlossDashboardComponent },
    ],
  },
  { path: '**', component: PageNotFoundComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SectionRoutingModule {}
