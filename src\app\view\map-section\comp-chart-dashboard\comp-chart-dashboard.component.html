<div>
  <div class="title">Terrain Analysis Results</div>
  <button class="fullscreen-btn" (click)="toggleFullscreen()">⤢</button>
  <button
    mat-mini-fab
    class="close-btn"
    matTooltip="Compare Map"
    [routerLink]="['/map', 'compare-map']">
    <mat-icon>arrow_back</mat-icon>
  </button>
  <div class="slideshow-container">
    <div
      *ngFor="let slide of slides; let i = index"
      class="slide"
      [class.active]="i === slideIndex">
      <div class="slide-title">{{ slide.title }}</div>
      <img [src]="base_path + slide.path" [alt]="slide.title" />
      <div class="description">{{ slide.description }}</div>
    </div>
    <div class="slide-number">{{ slideIndex + 1 }} / {{ slides.length }}</div>
    <a class="prev" (click)="changeSlide(-1)">❮</a>
    <a class="next" (click)="changeSlide(1)">❯</a>

    <div class="dots">
      <span
        *ngFor="let dot of slides; let i = index"
        class="dot"
        [ngClass]="{ actives: i === slideIndexdot }"></span>
    </div>
  </div>

  <div class="nav-hint">
    Use ← → arrow keys or click arrows to navigate • Press F for fullscreen
  </div>
</div>
