<div id="compare_map">
  <button mat-mini-fab class="close-btn" matTooltip="Map" [routerLink]="['/map']">
    <mat-icon>arrow_back</mat-icon>
  </button>
  <div class="button-center" *ngIf="isdateleftright; else dem_content">
    <mat-card class="select-text p-2">
      <mat-select
        name="dateleft"
        [(ngModel)]="dateleft"
        (ngModelChange)="updateSelectedDateData(dateleft, 'left')">
        <mat-option *ngFor="let dateleft of dateArray" [value]="dateleft">
          {{ dateleft }}
        </mat-option>
      </mat-select>
    </mat-card>
    <ng-container *ngIf="allDefects.length">
      <mat-card class="select-text p-2" style="width: 200px !important">
        <mat-select
          name="defect_left"
          [(ngModel)]="defect_left"
          (ngModelChange)="kmlName(defect_left, 'left')">
          <mat-option [value]="''">Turn KML Off</mat-option>
          <mat-option *ngFor="let defect of allDefects" [value]="defect">{{ defect }}</mat-option>
        </mat-select>
      </mat-card>
      <mat-card class="select-text p-2" style="width: 200px !important">
        <mat-select
          name="defect_right"
          [(ngModel)]="defect_right"
          (ngModelChange)="kmlName(defect_right, 'right')">
          <mat-option [value]="''">Turn KML Off</mat-option>
          <mat-option *ngFor="let defect of allDefects" [value]="defect">{{ defect }}</mat-option>
        </mat-select>
      </mat-card>
    </ng-container>
    <mat-card class="select-text p-2">
      <mat-select
        name="dateright"
        [(ngModel)]="dateright"
        (ngModelChange)="updateSelectedDateData(dateright, 'right')">
        <mat-option *ngFor="let dateright of dateArray" [value]="dateright">
          {{ dateright }}
        </mat-option>
      </mat-select>
    </mat-card>
  </div>
  <ng-template #dem_content>
    <div class="button-center">
      <mat-card class="select-text p-2" style="width: 150px !important">
        <mat-select
          name="leftPlan"
          [(ngModel)]="leftPlan"
          (ngModelChange)="updatePlanChange(leftPlan, 'left')">
          <mat-option *ngFor="let option of planOptions" [value]="option">
            {{ option }}
          </mat-option>
        </mat-select>
      </mat-card>
      <mat-card class="select-text p-2" style="width: 150px !important">
        <mat-select
          name="rightPlan"
          [(ngModel)]="rightPlan"
          (ngModelChange)="updatePlanChange(rightPlan, 'right')">
          <mat-option *ngFor="let option of planOptions" [value]="option">
            {{ option }}
          </mat-option>
        </mat-select>
      </mat-card>
    </div>
  </ng-template>
  <mat-icon
    matTooltip="Close Fullscreen"
    class="side-icons fullscreen"
    (click)="toggleFullscreen()"
    *ngIf="isFullscreen">
    close_fullscreen
  </mat-icon>
  <mat-icon
    matTooltip="Open Fullscreen"
    class="side-icons fullscreen"
    (click)="toggleFullscreen()"
    *ngIf="!isFullscreen">
    open_in_full
  </mat-icon>
  <mat-icon (click)="zoomreset()" class="side-icons reset" matTooltip="Zoom Reset">
    find_replace
  </mat-icon>
  <mat-icon (click)="zoomin()" class="side-icons in" matTooltip="Zoom In">zoom_in</mat-icon>
  <mat-icon (click)="zoomout()" class="side-icons out" matTooltip="Zoom Out">zoom_out</mat-icon>
  <mat-icon class="side-icons stacks" (click)="openModal(isModalOpen)" matTooltip="Open Layers">
    layers
  </mat-icon>
  <mat-icon
    (click)="toggleAllDefects()"
    class="side-icons anomalies"
    matTooltip="All Anomalies"
    *ngIf="allDefects.length">
    select_all
  </mat-icon>
  <ng-container *ngIf="!isdateleftright">
    <div class="dtmlegend" style="left: 40px" cdkDrag>
      <img [src]="legendleft" />
    </div>
    <div class="dtmlegend" style="right: 80px" cdkDrag>
      <img [src]="legendright" />
    </div>
  </ng-container>
  <ng-container *ngIf="legendshow">
    <div class="dtmlegend" style="left: 40px" cdkDrag>
      <img [src]="legendtemp" />
    </div>
  </ng-container>
  <div class="mapslayout" *ngIf="isModalOpen">
    <div class="drone-data-thermal" *ngIf="!isDEM">
      <span class="span-font">Drone Data</span>
      <div class="base-map-img">
        <div class="img-items">
          <img
            [src]="thermal"
            (click)="ViewMenu('thermal')"
            class="img-dim"
            [style.border-color]="selectedImage === 'thermal' ? 'darkcyan' : 'white'" />
          <span class="span-font">Thermal</span>
        </div>
        <div class="img-items">
          <img
            [src]="cad"
            (click)="ViewMenu('cad')"
            class="img-dim"
            [style.border-color]="selectedImage === 'cad' ? 'darkcyan' : 'white'" />
          <span class="span-font">CAD</span>
        </div>
      </div>
    </div>
    <div class="base-map">
      <span class="span-font">Base Map</span>
      <div class="base-map-img">
        <div class="img-items">
          <img
            [src]="default"
            class="img-dim"
            (click)="setBaseView('terrain', true)"
            [style.border-color]="selectedImage2 === 'terrain' ? 'darkcyan' : 'white'" />
          <span class="span-font">Terrain</span>
        </div>
        <div class="img-items">
          <img
            [src]="satellite"
            class="img-dim"
            (click)="setBaseView('satellite', true)"
            [style.border-color]="selectedImage2 === 'satellite' ? 'darkcyan' : 'white'" />
          <span class="span-font">Satellite</span>
        </div>
      </div>
    </div>
  </div>
  <mat-card
    class="dtmlegend"
    *ngIf="defect_left && legendColors[defect_left]?.left"
    style="top: 520px; left: 40px"
    cdkDrag>
    <div class="status-indication">
      <div
        class="indication-bar mx-2"
        [style.background-color]="legendColors[defect_left]?.left"></div>
      <span class="indication-text">{{ defect_left }}</span>
      <mat-slide-toggle
        [(ngModel)]="leftPolygonToggle"
        (change)="togglePolygonLayer('left', leftPolygonToggle)"></mat-slide-toggle>
    </div>
  </mat-card>
  <mat-card
    class="dtmlegend"
    *ngIf="defect_right && legendColors[defect_right]?.right"
    style="top: 520px; right: 80px"
    cdkDrag>
    <div class="status-indication">
      <div
        class="indication-bar mx-2"
        [style.background-color]="legendColors[defect_right]?.right"></div>
      <span class="indication-text">{{ defect_right }}</span>
      <mat-slide-toggle
        [(ngModel)]="rightPolygonToggle"
        (change)="togglePolygonLayer('right', rightPolygonToggle)"></mat-slide-toggle>
    </div>
  </mat-card>
  <ng-container *ngIf="allDefects.length && isAllDefects">
    <mat-card class="dtmlegend" style="top: 280px; left: 40px" cdkDrag>
      <div class="status-indication-all">
        <mat-slide-toggle
          class="mb-1 text-center"
          [(ngModel)]="leftPolygonToggle"
          (change)="togglePolygonLayer('left', leftPolygonToggle)"></mat-slide-toggle>
        <div class="d-flex align-items-center" *ngFor="let currentDefect of allDefects">
          <div
            class="indication-bar mx-2"
            [style.background-color]="legendColors[currentDefect]?.left"></div>
          <span class="indication-text">{{ currentDefect }}</span>
        </div>
      </div>
    </mat-card>
    <mat-card class="dtmlegend" style="top: 280px; right: 80px" cdkDrag>
      <div class="status-indication-all">
        <mat-slide-toggle
          class="mb-1 text-center"
          [(ngModel)]="rightPolygonToggle"
          (change)="togglePolygonLayer('right', rightPolygonToggle)"></mat-slide-toggle>
        <div class="d-flex align-items-center" *ngFor="let currentDefect of allDefects">
          <div
            class="indication-bar mx-2"
            [style.background-color]="legendColors[currentDefect]?.right"></div>
          <span class="indication-text">{{ currentDefect }}</span>
        </div>
      </div>
    </mat-card>
  </ng-container>
  <mat-card class="compare-two" *ngIf="popup_card_visibility">
    <mat-card-header class="p-0">
      <div
        class="d-flex justify-content-between align-items-center w-100 mx-3 mb-2"
        style="border-bottom: 2px dashed #282828">
        <h2 class="m-0 font-weight-bold">Defect Details</h2>
        <button mat-icon-button (click)="popup_card_visibility = false">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </mat-card-header>
    <mat-card-content style="padding: 0px; font-size: 12px">
      <div class="row m-1" *ngFor="let item of popupData | keyvalue">
        <div class="col-6 px-1">
          <p style="font-weight: 600" class="m-0">{{ item.key }}</p>
        </div>
        <div class="col-6 px-1" style="word-break: break-all">
          <ng-container *ngIf="isImage(item.value)">
            <img style="width: 75px; height: 75px" mat-card-image [src]="item.value" />
          </ng-container>
          <ng-container *ngIf="!isImage(item.value)">
            {{ item.value }}
          </ng-container>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
  <ng-container *ngIf="isDEM">
    <div
      *ngFor="let icon of icons; let i = index"
      class="side-icons"
      [ngClass]="'icons-placing' + i">
      <div class="icon-wrapper">
        <img
          [src]="icon.imageSrc"
          (click)="ViewMenu(icon.menuType)"
          (mouseenter)="icon.showTooltip = true"
          (mouseleave)="icon.showTooltip = false"
          alt="Icon" />
      </div>
      <div class="comparison-card" *ngIf="icon.showTooltip">
        <h4 class="font-weight-bolder mb-2">{{ icon.title }}</h4>
        <p class="m-0">{{ icon.description }}</p>
      </div>
    </div>
  </ng-container>
</div>
