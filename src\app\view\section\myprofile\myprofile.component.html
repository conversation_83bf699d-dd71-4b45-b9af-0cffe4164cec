<div class="container">
  <mat-tab-group *ngIf="users">
    <mat-tab label="My Profile">
      <form [formGroup]="profileForm">
        <div class="row mt-3">
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Company</mat-label>
              <input matInput name="company" formControlName="company" />
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Role</mat-label>
              <input matInput name="role" formControlName="role" />
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Email Address</mat-label>
              <input matInput name="email" formControlName="email" type="email" />
              <mat-error *ngIf="hasError('email', 'required')">Email is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">First Name</mat-label>
              <input
                matInput
                name="first_name"
                formControlName="first_name"
                placeholder="Enter your first name" />
              <mat-error *ngIf="hasError('first_name', 'required')">
                First name is required
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Last Name</mat-label>
              <input
                matInput
                name="last_name"
                formControlName="last_name"
                placeholder="Enter your last name" />
              <mat-error *ngIf="hasError('last_name', 'required')">Last name is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Mobile Number</mat-label>
              <input
                matInput
                type="number"
                placeholder="Enter your mobile number"
                name="contact"
                formControlName="contact" />
              <mat-error *ngIf="hasError('contact', 'required')">
                Mobile number is required
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Country</mat-label>
              <mat-select
                placeholder="country"
                (selectionChange)="selectCountry($event.value)"
                name="country"
                formControlName="country">
                <mat-option value="" selected>Select Country</mat-option>
                <mat-option disabled *ngIf="users.country" [value]="users.country">
                  {{ users.country }}
                </mat-option>
                <mat-option *ngFor="let country of countries" [value]="country.isoCode">
                  {{ country.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="hasError('country', 'required')">Country is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">State</mat-label>
              <mat-select
                placeholder="state"
                (selectionChange)="selectState($event.value)"
                name="state"
                formControlName="state">
                <mat-option value="" selected>Select State</mat-option>
                <mat-option *ngIf="users.state" [value]="users.state">
                  {{ users.state }}
                </mat-option>
                <mat-option *ngFor="let state of states_data" [value]="state.isoCode">
                  {{ state.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="hasError('state', 'required')">State is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">City</mat-label>
              <mat-select placeholder="city" name="city" formControlName="city">
                <mat-option value="" selected>Select City</mat-option>
                <mat-option *ngIf="users.city" [value]="users.city">
                  {{ users.city }}
                </mat-option>
                <mat-option *ngFor="let city_data of cities_data" [value]="city_data.name">
                  {{ city_data.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="hasError('city', 'required')">City is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-4">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Pincode</mat-label>
              <input
                type="number"
                matInput
                placeholder="Enter your pincode"
                name="pincode"
                formControlName="pincode" />
              <mat-error *ngIf="hasError('pincode', 'required')">Pincode is required</mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="text-center">
          <button
            mat-button
            class="w-25 text-uppercase"
            (click)="updateDetails()"
            [disabled]="!profileForm.valid || Isworking">
            <span *ngIf="!Isworking">Update</span>
            <span *ngIf="Isworking">
              <div class="spinner-border" role="status" *ngIf="Isworking"></div>
            </span>
          </button>
        </div>
      </form>
    </mat-tab>
    <mat-tab label="Reset Password">
      <form [formGroup]="resetForm">
        <div class="row mt-3">
          <div class="col-lg-6">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">New Password</mat-label>
              <input
                matInput
                placeholder="Enter your new password"
                [type]="hide2 ? 'password' : 'text'"
                name="new_password"
                formControlName="password"
                (input)="checkPassword()" />
              <mat-icon matSuffix (click)="hide2 = !hide2">
                {{ hide2 ? 'visibility_off' : 'visibility' }}
              </mat-icon>
              <mat-error *ngIf="hasError2('password', 'required')">Password is required</mat-error>
            </mat-form-field>
          </div>
          <div class="col-lg-6">
            <mat-form-field appearance="outline" class="w-100 my-2">
              <mat-label class="field-content">Retype New Password</mat-label>
              <input
                matInput
                placeholder="Confirm new password"
                [type]="hide3 ? 'password' : 'text'"
                name="retype_password"
                formControlName="confirm_password"
                (input)="checkPassword()" />
              <mat-icon matSuffix (click)="hide3 = !hide3">
                {{ hide3 ? 'visibility_off' : 'visibility' }}
              </mat-icon>
              <mat-error *ngIf="hasError2('confirm_password', 'required')">
                Confirm Password is required
              </mat-error>
              <mat-hint *ngIf="!isValidPassword" style="color: red">
                Password and Confirm password doesn't match
              </mat-hint>
            </mat-form-field>
          </div>
        </div>
        <div class="text-center">
          <button
            mat-button
            class="w-25 text-uppercase"
            (click)="resetPassword()"
            [disabled]="!resetForm.valid || Isworking">
            <span *ngIf="!Isworking">Reset</span>
            <span *ngIf="Isworking">
              <div class="spinner-border" role="status" *ngIf="Isworking"></div>
            </span>
          </button>
        </div>
      </form>
    </mat-tab>
  </mat-tab-group>
</div>
