import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-share',
  templateUrl: './share.component.html',
  styleUrls: ['./share.component.css'],
})
export class ShareComponent implements OnInit {
  displayedColumns: string[] = ['name', 'email', 'role', 'access'];
  dataSource = new MatTableDataSource<any>([]);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  Isworking = false;

  constructor(
    private apiConfigService: ApiConfigService,
    private sharedDataService: SharedDataService,
    private toastr: ToastrService,
    private dialogRef: MatDialogRef<ShareComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    const usersWithAccessSet = new Set(this.data.usersWithAccess.map(user => user.db_id));
    this.data.allUsers.forEach(user => {
      user.access = usersWithAccessSet.has(user.db_id);
    });
    setTimeout(() => {
      this.dataSource.data = this.data.allUsers;
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }, 200);
  }
  updateRole(user) {
    this.Isworking = true;
    const user_data = {
      db_id: user.db_id,
      project_id: this.data.projectId,
      access: user.access,
    };
    this.apiConfigService.addProjectUser(user_data).subscribe(
      (res: any) => {
        if (res.status == 'success') {
          this.toastr.success(res.message);
          this.sharedDataService.remove('get_project_by_category');
          this.Isworking = false;
        }
      },
      err => {
        this.Isworking = false;
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        console.error(err);
      }
    );
  }
  onClose() {
    this.dialogRef.close();
  }
}
