import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class ApiConfigService {
  constructor(private http: HttpClient) {}

  getUserDetails() {
    return `${environment.carnot_url}accounts/user_details/`;
  }

  getDashboardData() {
    return `${environment.carnot_url}project/get_dashboard_data`;
  }

  getAllProjects(filter) {
    return `${environment.carnot_url}project/get_all?filter=${JSON.stringify(filter)}`;
  }

  getProjectByID(id) {
    return `${environment.carnot_url}project/get_project/${id}`;
  }

  getRoles() {
    return `${environment.carnot_url}accounts/role/`;
  }

  getProjectData(project_id) {
    return `${environment.carnot_url}project/get_project/${project_id}`;
  }

  getProjectByCategory() {
    return `${environment.carnot_url}project/get_project_by_category`;
  }

  getWeather(lat, lon) {
    return `${environment.carnot_url}project/get_weather/?lat=${lat}&lon=${lon}`;
  }

  getManageUsers(group_id) {
    return `${environment.carnot_url}accounts/manage_users/${group_id}`;
  }

  isEmailUnique(data) {
    return this.http.post(`${environment.carnot_url}accounts/is_email_unique/`, data);
  }

  register(data) {
    return this.http.post(`${environment.carnot_url}accounts/user_register/`, data);
  }

  getDefectData(project_id, project_date, params) {
    const { page, pageSize, sortField, sortDirection, filters } = params;
    let url = `${environment.carnot_url}project/get_defects/${project_id}/${project_date}?page=${page + 1}&page_size=${pageSize}`;
    if (sortField && sortDirection) {
      url += `&sort_by=${sortField}&sort_direction=${sortDirection}`;
    }
    if (filters) {
      if (filters.globalFilter) url += `&search=${filters.globalFilter}`;
      if (filters.defect) url += `&defect=${filters.defect}`;
      if (filters.block) url += `&block=${filters.block}`;
      if (filters.table) url += `&table=${filters.table}`;
      if (filters.severity) url += `&severity=${filters.severity}`;
      if (filters.version) url += `&version=${filters.version}`;
    }
    return this.http.get(url);
  }

  createDefectData(data) {
    return this.http.post(`${environment.carnot_url}project/process_defects/upload`, data);
  }

  getAOI() {
    return this.http.get(`${environment.carnot_url}draw/save_aoi/`);
  }

  addProjectUser(data) {
    return this.http.put(`${environment.carnot_url}project/add_project_users/`, data);
  }

  roleAccess(data) {
    return this.http.put(`${environment.carnot_url}accounts/role_access/`, data);
  }

  activeAccess(data) {
    return this.http.put(`${environment.carnot_url}accounts/active_access/`, data);
  }

  updateUserDetails(data) {
    return this.http.put(`${environment.carnot_url}accounts/user_update/`, data);
  }

  resetPassword(data) {
    return this.http.put(`${environment.carnot_url}accounts/reset_password/`, data);
  }

  generateLink(data) {
    return this.http.post(`${environment.carnot_url}accounts/generate_link/`, data);
  }

  getDrawtools(project_id, date) {
    return this.http.get(`${environment.carnot_url}drawtool/get_data/${project_id}/${date}`);
  }

  createDrawtool(drawtool) {
    return this.http.post(`${environment.carnot_url}drawtool/save`, drawtool);
  }

  updateDrawtool(drawtool) {
    return this.http.put(`${environment.carnot_url}drawtool/update/${drawtool.id}`, drawtool);
  }

  deleteDrawtool(id: number) {
    return this.http.delete<void>(`${environment.carnot_url}drawtool/delete/${id}`);
  }

  getSignedUrl(bucket, key) {
    return this.http.get(
      `${environment.ftp_url}s3/files/presigned-url?bucket_name=${bucket}&key=${key}`
    );
  }
}
