import { HttpService } from '@/view/map-section/services-map/http.service';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.css'],
})
export class ReportsComponent {
  block_type: string = null;
  inverter_type_arr: string[];
  file_type_arr: string[];
  inverter_type: string = null;
  file_type: string = null;
  file_url: string;
  public fileObj: any = {};

  constructor(
    private _http: HttpService,
    private toastr: ToastrService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<ReportsComponent>
  ) {
    this.fileObj = this.data.fileObj;
  }
  async downloadFile() {
    const { block_type, file_type, inverter_type, fileObj } = this;
    if (block_type && file_type) {
      this.file_url =
        block_type === 'SUMMARY'
          ? fileObj[block_type]?.[file_type]
          : block_type === 'INVERTER' && inverter_type
            ? fileObj[block_type]?.[inverter_type]?.[file_type]
            : null;
    }
    if (!this.file_url) {
      this.toastr.error('', 'File URL is not available');
      return;
    }
    const urlParts = this.file_url.split('/');
    const fileKey = urlParts.slice(3).join('/');
    const fileExtension = fileKey.split('.').pop()?.toLowerCase();
    const signedUrl = await this._http.getSignedUrl(this.file_url);
    if (signedUrl) {
      this.toastr.info('Please wait..', 'Downloading Started!!!');
      if (fileExtension === 'pdf') {
        window.open(signedUrl, '_blank');
      } else {
        window.location.assign(signedUrl);
      }
    } else {
      this.toastr.error('Please try again..', 'Downloading Failed!!!');
    }
  }
  checkData() {
    if (this.file_type) this.file_type = null;

    const blockData = this.fileObj[this.block_type];
    if (this.block_type === 'SUMMARY' && blockData) {
      this.file_type_arr = this.objectKeys(blockData);
    }
    if (this.block_type === 'INVERTER' && blockData) {
      this.inverter_type_arr = this.objectKeys(blockData);
      if (this.inverter_type) {
        this.file_type_arr = this.objectKeys(blockData[this.inverter_type] ?? {});
      }
    }
  }
  objectKeys(obj: any): string[] {
    if (obj == null) return [];
    return Object.keys(obj);
  }
  onClose() {
    this.dialogRef.close();
  }
}
