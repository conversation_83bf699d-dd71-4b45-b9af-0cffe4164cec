<div class="data">
  <video
    #videoPlayer
    class="w-100"
    (timeupdate)="updateTime()"
    (loadedmetadata)="setVideoDuration()">
    <source src="{{ data }}" type="video/mp4" />
    Your browser does not support the video tag.
  </video>
  <div class="d-flex align-items-center justify-content-between mt-2">
    <button mat-mini-fab (click)="togglePlayPause()">
      <span *ngIf="isPlaying; else elseBlock">
        <mat-icon matTooltip="Pause">pause</mat-icon>
      </span>
      <ng-template #elseBlock>
        <mat-icon matTooltip="Play">play_arrow</mat-icon>
      </ng-template>
    </button>
    <input
      type="range"
      min="0"
      [max]="videoDuration"
      step="0.1"
      [(ngModel)]="currentTime"
      (input)="seekVideo()"
      class="vdo-input" />
    <span class="font-weight-bolder">
      {{ formatTime(currentTime) }} / {{ formatTime(videoDuration) }}
    </span>
  </div>
  <mat-dialog-actions class="justify-content-evenly">
    <button mat-button (click)="onClose()" class="w-25 text-uppercase">Close</button>
  </mat-dialog-actions>
</div>
