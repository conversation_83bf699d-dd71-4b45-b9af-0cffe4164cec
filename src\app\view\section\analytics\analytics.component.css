.exam2 {
  width: 175px;
  border: 2px solid var(--primary);
  border-radius: 5px;
  padding: 5px;
}
.anal-graphs,
.analytics-card {
  border: 1px solid #cbcee8;
  box-shadow: 0 12px 11px -3px rgba(113, 126, 195, 0.25);
}
.analytics-container {
  margin: 0.5rem;
}
.anal-graphs {
  margin-bottom: 0.5rem;
}
.analytics-card {
  padding: 0;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}
.rowcard2,
.stat-card {
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.1);
}
.analytics-card-title {
  border-bottom: 3px solid #cbcee8;
  margin-bottom: 10px;
}
.page-title {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
}
.project-name {
  margin-left: 0.5rem;
  font-weight: 600;
}
.dash-cont {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 1rem 0;
}
.rowcard2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 2px solid #f2f2f2;
  border-radius: 10px;
  width: 375px;
}
.dashboard-container,
.graph-card,
.stat-card {
  display: flex;
}
.dashboard-container {
  flex-wrap: wrap;
  gap: 0.5rem;
}
.stat-card {
  flex: 1 1 300px;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 2px solid #f2f2f2;
}
.stat-content {
  display: flex;
  flex-direction: column;
}
.stat-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}
.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: var(--primary);
}
.stat-subtitle {
  font-size: 0.9rem;
  color: #666;
}
.stat-icon {
  background-color: var(--primary);
  color: #fff;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.sub-dashcont {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.analytics-cont {
  font-size: 1.2rem;
  font-weight: 700;
}
.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
}
.metric-sub {
  font-size: 1rem;
}
.no-project,
.title {
  font-weight: 600;
  font-size: 18px;
  text-align: center;
}
.graph-card {
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem;
  background-color: #f7f7f7;
  border-radius: 10px;
  align-items: stretch;
  justify-content: space-between;
}
.graph-container {
  flex: 1;
  min-width: 280px;
  overflow: visible;
  width: 100%;
  height: 100%;
}
.statistic {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 10px;
  flex: 0 0 30%;
  min-width: 250px;
}
.banner {
  width: 100%;
  border-radius: 10px 10px 0 0;
  margin-bottom: 10px;
}
.title {
  text-transform: uppercase;
}
.details {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
  cursor: default;
}
.key {
  font-weight: 600;
  width: 40%;
  margin: 0 5px;
}
.value {
  width: 60%;
}
#chart10,
#chart11,
#chart3,
#chart4,
#chart5,
#chart6,
#chart7,
#chart8 {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
#ana_summary_cou,
#ana_summary_cou_1,
#ana_summary_cou_desc_1,
#ana_summary_cou_perc_1,
#ana_summary_def,
#ana_summary_def_1,
#summary_cou,
#summary_def,
.countValue {
  transition: 0.3s;
}
.no-project {
  margin: 1rem 0;
}
.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.2rem;
  color: #666;
  font-weight: 500;
}
plotly-plot {
  width: 100%;
  height: 100%;
  min-height: 400px;
  display: block;
}
.js-plotly-plot,
.plot-container {
  width: 100%;
  height: 100%;
}
.js-plotly-plot .plotly,
.js-plotly-plot .plotly div {
  pointer-events: all;
}
.graph-panel {
  flex: 1 1 300px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  min-height: 400px;
}
.graph-panel.defect-details {
  flex: 1 1 250px;
}
.defect-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}
.defect-detail {
  display: flex;
  margin-bottom: 0.75rem;
}
.graph-layout,
.input-group {
  margin-bottom: 0.5rem;
  display: flex;
}
.detail-key {
  flex: 1;
  font-weight: 500;
  color: #555;
}
.detail-value {
  flex: 1;
  font-weight: 600;
  color: #333;
}
.graph-layout {
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem;
}
.losses-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}
.loss-panel {
  flex: 1 1 45%;
}
.input-group {
  justify-content: center;
  align-items: center;
}
.input-group label {
  flex: 1;
  font-weight: 500;
}
.input-control {
  flex: 1;
  display: flex;
  align-items: center;
}
.input-control input {
  width: 100px;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 0.5rem;
}
.result-card {
  background-color: #e9e9e9;
  text-align: center;
  border-radius: 10px;
  margin: 1rem 0;
  padding: 1rem;
  font-weight: 700;
}
.ppm-chart,
.ppm-data,
.ppm-panel {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
}
.result-label {
  font-size: 1rem;
  color: #555;
  margin-bottom: 0.5rem;
}
.result-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
}
.data-table th,
.panel-title {
  color: #333;
  font-weight: 600;
}
.data-table {
  width: 100%;
  margin-top: 1rem;
}
.data-table td,
.data-table th {
  padding: 0.75rem;
  text-align: left;
}
.data-table .value-cell {
  font-weight: 600;
  text-align: right;
}
.data-table .unit-cell {
  text-align: left;
  color: #666;
}
.ppm-title {
  text-align: center;
  margin: 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}
.ppm-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}
.ppm-panel {
  flex: 1 1 30%;
}
.panel-title {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  text-align: center;
}
.ppm-table {
  width: 100%;
}
.ppm-table tr td {
  padding: 0.5rem;
  height: auto;
  width: auto;
}
.ppm-table tr td:first-child {
  font-weight: 600;
  width: 50%;
}
.ppm-table tr td:nth-child(2) {
  font-weight: 600;
  width: 25%;
}
.ppm-table tr td:last-child {
  width: 25%;
}
.ppm-chart-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}
.ppm-data {
  flex: 1 1 20%;
}
.ppm-chart {
  flex: 1 1 50%;
  min-height: 400px;
}
@media (max-width: 767px) {
  .analytics-container {
    margin: var(--spacing-xs);
  }
  .exam2 {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }
  .page-title {
    font-size: var(--font-lg);
  }
  .graph-container,
  .statistic {
    width: 100%;
  }
  .graph-panel,
  .statistic {
    margin-bottom: var(--spacing-sm);
  }
  .dash-cont {
    flex-direction: column;
    align-items: center;
    margin: var(--spacing-sm) 0;
  }
  .dashboard-container,
  .graph-card,
  .graph-layout,
  .losses-container,
  .ppm-chart-container,
  .ppm-layout {
    flex-direction: column;
  }
  .statistic {
    flex: 1 1 100%;
  }
  .graph-container,
  .graph-panel {
    flex: 1 1 100%;
    min-height: 300px;
  }
  .graph-panel.defect-details {
    flex: 1 1 100%;
  }
  .ppm-panel,
  .stat-card {
    flex: 1 1 100%;
    margin-bottom: var(--spacing-sm);
  }
  .loss-panel,
  .ppm-chart,
  .ppm-data {
    flex: 1 1 100%;
    margin-bottom: var(--spacing-md);
  }
  .rowcard2 {
    width: 100%;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
  }
  .stat-value {
    font-size: var(--font-lg);
  }
  .metric-value {
    font-size: var(--font-lg);
  }
  .analytics-cont {
    font-size: var(--font-md);
  }
  .input-control input {
    width: 80px;
  }
  .details {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  .key,
  .value {
    width: 100%;
  }
}
