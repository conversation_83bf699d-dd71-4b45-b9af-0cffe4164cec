import { AuthService } from '@/controller/auth.service';
import { LoadingService } from '@/controller/loading.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Observable, from, throwError } from 'rxjs';
import { catchError, finalize, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class HttpInterceptor {
  constructor(
    private authService: AuthService,
    private sharedDataService: SharedDataService,
    private loadingService: LoadingService,
    private toastr: ToastrService
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (request.method === 'GET') this.loadingService.showLoading();

    return from(this.handleAccess(request)).pipe(
      switchMap(modifiedRequest => next.handle(modifiedRequest)),
      catchError(error => this.handleError(error)),
      finalize(() => {
        if (request.method === 'GET') this.loadingService.hideLoading();
      })
    );
  }
  private async handleAccess(request: HttpRequest<any>): Promise<HttpRequest<any>> {
    const isLoggedIn = await this.authService.isLoggedIn();
    if (isLoggedIn) {
      const token = await this.authService.getToken();
      return request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`,
        },
      });
    }
    return request;
  }
  private handleError(error: any): Observable<never> {
    if (error instanceof HttpErrorResponse) {
      if (error.status === 401 || error.status === 403) {
        this.sharedDataService.clearData();
        this.toastr.error('Please login again', 'Session Expired!');
      }
    }
    return throwError(() => error);
  }
}
