<div id="scroller" data-spy="scroll" data-target="#scroll-spy">
  <header class="header js-header-scroll">
    <nav>
      <div class="flex justify-between" id="scroll-spy">
        <h1 class="carnot-heading">Carnot</h1>
        <ul class="header-main2">
          <li id="menu-home">
            <a (click)="scrollToPosition('home')">Home</a>
          </li>
          <li id="menu-features">
            <a (click)="scrollToPosition('features')">Features</a>
          </li>
          <div class="header-btn">
            <div
              [routerLink]="['/auth/login']"
              class="btn btns-primary btn-rounded btn-xs btn-header">
              Log In / Sign Up
            </div>
          </div>
        </ul>
      </div>
    </nav>
    <nav class="mobile-menu" *ngIf="!isMenuActive">
      <div class="menu-toggle" (click)="toggleMenu()">
        <div class="bar"></div>
        <div class="bar"></div>
        <div class="bar"></div>
      </div>
    </nav>
    <div class="menu-modal" *ngIf="isMenuActive">
      <div class="modal-content">
        <div class="close-button" (click)="toggleMenu()">
          <span mat-list-item (click)="scrollToPosition('home')">
            <mat-icon class="menu-icon">close</mat-icon>
          </span>
        </div>
        <ul class="menu-items">
          <li>
            <a mat-list-item (click)="scrollToPosition('home')">
              <mat-icon class="menu-icon" style="padding-top: 8px">home</mat-icon>
              <span class="menu-text" style="margin-left: 8px">Home</span>
            </a>
          </li>
          <li>
            <a mat-list-item (click)="scrollToPosition('features')">
              <mat-icon class="menu-icon" style="padding-top: 8px; margin-top: 4px">
                featured_play_list
              </mat-icon>
              <span class="menu-text" style="margin-left: 8px">Features</span>
            </a>
          </li>
          <li>
            <a mat-list-item [routerLink]="['/auth', 'login']">
              <mat-icon class="menu-icon" style="padding-top: 8px; color: #fff; margin-top: 4px">
                contacts
              </mat-icon>
              <span class="menu-text" style="color: #fff; margin-left: 8px">Log In</span>
            </a>
          </li>
          <li>
            <a mat-list-item [routerLink]="['/auth', 'register']">
              <mat-icon class="menu-icon" style="padding-top: 8px; color: #fff; margin-top: 4px">
                person
              </mat-icon>
              <span class="menu-text" style="color: #fff; margin-left: 8px">Sign Up</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </header>
  <section class="masthead js-masthead-height" id="home">
    <div class="masthead__style-overlay"></div>
    <div class="masthead__style-watter">
      <div class="container">
        <div class="row">
          <div class="col-lg-8 offset-lg-2 text-center">
            <div class="masthead__content">
              <h1 class="masthead__content-title" style="margin-top: 1rem">
                The Powerful Utility-Scale Solar PV Inspection Platform
              </h1>
              <p class="masthead__content-subtitle">
                A one-stop solution for your solar asset monitoring, Improve solar PV performance &
                asset health using our Enterprise AI visual Analytics Platform
              </p>
            </div>
            <!-- <div class="masthead__content">
              <button
                style="color: white; "
                [routerLink]="['/auth/register']"
                class="btns btns-primary btns-rounded btns-icon-left btns-lg mb-2">
                Get Started
              </button>
            </div> -->
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12 text-center">
            <div class="video__background">
              <div class="video__background-wrapper js-video-background-wrapper">
                <video
                  class="video-player"
                  controls
                  poster="assets/images/<EMAIL>"
                  loop
                  muted
                  autoplay
                  oncanplay="this.play()"
                  onloadedmetadata="this.muted = true">
                  <source src="{{ demo_video }}" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              <img
                src="assets/images/<EMAIL>"
                class="video__background-image js-video-background-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="section" id="features">
    <div class="container">
      <div class="row">
        <div class="col">
          <div class="tabs nav tabs__navigation">
            <div class="icon-container">
              <mat-icon class="custom-icon-size">insert_drive_file</mat-icon>
              <mat-icon class="custom-icon-size">av_timer</mat-icon>
              <mat-icon class="custom-icon-size">table_chart</mat-icon>
              <mat-icon class="custom-icon-size">file_copy</mat-icon>
            </div>
            <mat-tab-group animationDuration="1000ms">
              <mat-tab label="Defect Classification">
                <div
                  class="tab-pane"
                  id="getstarted"
                  role="tabpanel"
                  aria-labelledby="getstarted-tab">
                  <div class="row">
                    <div class="col-lg-8 order-lg-4 mb-40">
                      <img src="assets/images/Carnot.png" class="img-fluid" alt="App screen" />
                    </div>
                    <div class="col-lg-4 align-self-center mb-40" style="left: 20px">
                      <h2>Defect Classification</h2>
                      <p>
                        Classify various types of PV defects and damages and characterize hotspots
                        based on IEC 62446-3 Standard.
                      </p>
                      <a
                        href="https://datasee.ai/contact"
                        class="btns btns-primary btns-rounded btns-stroke">
                        Learn more
                      </a>
                    </div>
                  </div>
                </div>
              </mat-tab>
              <mat-tab label="Accurate Measurements">
                <div class="tab-pane" id="feature" role="tabpanel" aria-labelledby="feature-tab">
                  <div class="row">
                    <div class="col-lg-8 order-lg-4 mb-40">
                      <img src="assets/images/Carnot2.png" class="img-fluid" alt="App screen" />
                    </div>
                    <div class="col-lg-4 align-self-center mb-40" style="left: 20px">
                      <h2>Accurate Measurements</h2>
                      <p>
                        Precisely locate anomalies and evaluate power loss metrics, panel
                        degradation rate, and more.
                      </p>
                      <a
                        href="https://datasee.ai/contact"
                        class="btn btns-primary btn-rounded btn-stroke">
                        Learn more
                      </a>
                    </div>
                  </div>
                </div>
              </mat-tab>
              <mat-tab label="Historical Analysis">
                <div
                  class="tab-pane"
                  id="scheduling"
                  role="tabpanel"
                  aria-labelledby="scheduling-tab">
                  <div class="row">
                    <div class="col-lg-8 order-lg-4 mb-40">
                      <img src="assets/images/Carnot3.png" class="img-fluid" alt="App screen" />
                    </div>
                    <div class="col-lg-4 align-self-center mb-40" style="left: 20px">
                      <h2>Historical Analysis</h2>
                      <p>
                        Historical data collection on cloud to review & assess panel degradation
                        levels.
                      </p>
                      <a
                        href="https://datasee.ai/contact"
                        class="btn btns-primary btn-rounded btn-stroke">
                        Learn more
                      </a>
                    </div>
                  </div>
                </div>
              </mat-tab>
              <mat-tab label="Reports">
                <div class="tab-pane" id="location" role="tabpanel" aria-labelledby="location-tab">
                  <div class="row">
                    <div class="col-lg-8 order-lg-4 mb-40">
                      <img src="assets/images/Carnot4.png" class="img-fluid" alt="App screen" />
                    </div>
                    <div class="col-lg-4 align-self-center mb-40" style="left: 20px">
                      <h2>Reports</h2>
                      <p>Criticality based reporting for priority based problem solving.</p>
                      <a
                        href="https://datasee.ai/contact"
                        class="btn btns-primary btn-rounded btn-stroke">
                        Learn more
                      </a>
                    </div>
                  </div>
                </div>
              </mat-tab>
            </mat-tab-group>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="section">
    <div class="container">
      <div class="row">
        <div class="col-lg-6 offset-lg-3">
          <h2 class="section__heading section__heading-center">Aerial Thermography Solution</h2>
        </div>
      </div>
      <div class="row">
        <div class="col-lg-6 offset-lg-3 text-center">
          <p>
            Ascertain a Solar PV plant’s health with Datasee.AI’s intuitive thermographic
            visualizations. With precise identification of hotspots, you can easily isolate the
            defective panels and gain insights on localized fixes, thereby assuring minimal power
            loss.
          </p>
          <a href="#" class="btn btns-primary btn-rounded">Register for a demo</a>
        </div>
      </div>
    </div>
  </section>
  <section class="section">
    <div class="container">
      <div class="row">
        <div class="col-lg-4">
          <div class="section__column section__column-left">
            <span class="icon-1d icon-map column-icon"></span>
            <h4>AI Defect Recognition</h4>
            <p>
              The precise location of the critical hotspots is identified and classified using our
              trained ML algorithms.
            </p>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="section__column section__column-left">
            <span class="icon-1d icon-chart column-icon"></span>
            <h4>IEC Standard</h4>
            <p>Classify hotspots, critical defects, and damages as per IEC TS 62446-3 standard.</p>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="section__column section__column-left">
            <span class="icon-1d icon-compare column-icon"></span>
            <h4>Inverter wise Analytics</h4>
            <p>
              Visualize the performance metrics and critical defects summary located on each
              inverter.
            </p>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="section__column section__column-left">
            <span class="icon-1d icon-3d column-icon"></span>
            <h4>Cloud Interface</h4>
            <p>
              Visualize all assets in the web portal and download inspection reports from anywhere
              in the world.
            </p>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="section__column section__column-left">
            <span class="icon-1d icon-hotspot column-icon"></span>
            <h4>Deep Analytics</h4>
            <p>
              More analytical features like power loss calculation, anomaly classifications,
              historical analysis, etc.
            </p>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="section__column section__column-left">
            <span class="icon-1d icon-question column-icon"></span>
            <h4>Superimposed Images</h4>
            <p>Thermal images are superimposed on RGB images to visualize the hotspots location.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="section">
    <div class="container">
      <div
        class="section__cta section__cta-column section__cta-offset"
        style="background-color: darkcyan">
        <div class="row">
          <div class="col-md-12">
            <h2>Subscribe to our Newsletter</h2>
          </div>
        </div>
        <div class="email-container">
          <div>
            <fieldset class="section__cta-subscribe">
              <form name="ctaSubscribe" action="#">
                <input
                  type="text"
                  class="section__cta-subscribe-input width-relative-to-parent-input"
                  placeholder="Enter your email address" />
                <button
                  class="btn btn-rounded btn-white section__cta-subscribe-button btn-icon-right width-relative-to-parent"
                  style="display: flex; justify-content: center; align-items: center">
                  Subscribe&nbsp;&nbsp;
                  <mat-icon>ads_click</mat-icon>
                </button>
              </form>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </section>
  <div class="back-to-top js-back-to-top" id="scrollToStart" (click)="scrollToPosition('home')">
    <mat-icon>arrow_circle_up</mat-icon>
  </div>
  <footer class="footer">
    <div class="container">
      <div class="row">
        <div class="col-lg-4">
          <div class="footer__widget">
            <h1>Datasee.AI Inc.</h1>
            <p>2946 Dale Drive, NE Atlanta, GA 30305.</p>
            <div class="d-flex">
              <mat-icon>phone</mat-icon>
              &nbsp;&nbsp;
              <p>******-695-0970</p>
            </div>
            <div class="d-flex">
              <mat-icon>email</mat-icon>
              &nbsp;&nbsp;
              <p>info&#64;datasee.ai</p>
            </div>
          </div>
        </div>
        <div class="col-6 col-lg-3 col-sm-3">
          <div class="footer__widget">
            <h4 class="footer__widget-title">Connect with us</h4>
            <ul class="footer__widget-network">
              <li>
                <a
                  href="https://in.linkedin.com/company/datasee-ai"
                  class="footer__widget-network-link">
                  <!-- <i class="fa fa-linkedin"></i> -->
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col">
          <div class="row">
            <div class="col-md-6">
              <p>Datasee.AI © 2023. All Rights Reserved.</p>
            </div>
            <div class="col-md-6 text-right">
              <ul class="footer__subfooter-liststyle">
                <li>
                  <a href="https://datasee.ai/contact">Help</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>
