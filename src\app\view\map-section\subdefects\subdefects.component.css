.image-title,
.info-value,
.type_btn {
  color: var(--primary);
}
.no-image mat-icon,
mat-icon {
  height: 32px;
  width: 32px;
  font-size: 32px;
}
.compact-sidebar {
  position: fixed;
  box-shadow: 0 3px 6px var(--shadow);
  left: 395px;
  width: 275px;
  background: #fff;
}
.sidebar-menu {
  overflow-y: scroll;
  height: 100vh;
}
.type {
  display: flex;
  width: 70%;
  justify-content: center;
  padding: 2px;
  flex-direction: column;
}
.no-image p,
.type p {
  margin: 0;
  font-size: 11px;
}
.info-value,
.type span {
  font-weight: 600;
  font-size: 11px;
}
.type_btn {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 30%;
  gap: 5px;
}
.main-card {
  display: flex;
  flex-direction: row;
  margin: 5px;
  padding: 5px;
  font-size: 12px;
  border: 3px solid;
}
.close-btn {
  font-size: 22px;
  position: fixed;
  margin-bottom: 4px;
  margin-right: 2px;
  text-align: left;
  z-index: 1200;
  background-color: var(--white);
  border: #fff;
  width: 200px;
  box-shadow: 0 5px 1px 1px #9999990d;
}
@media (max-width: 767px) {
  .close-btn {
    width: calc(100% - var(--spacing-md));
    font-size: var(--font-lg);
    margin: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  .main-card {
    flex-direction: column;
    margin: var(--spacing-xs);
    padding: var(--spacing-xs);
    font-size: var(--font-sm);
  }
  .defect-info,
  .image-box {
    margin-bottom: var(--spacing-sm);
  }
  .image-modal-container {
    padding: var(--spacing-sm);
  }
}
@media (min-width: 767px) {
  .isVisible500 {
    visibility: hidden;
  }
}
mat-icon {
  cursor: pointer;
}
.defect-info,
.image-box {
  background-color: #f5f5f5;
  border-radius: 5px;
  display: flex;
}
.image-modal-container {
  position: relative;
  padding: 0;
  max-width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.defect-info {
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 10px;
}
.info-item {
  margin: 0 10px;
  display: flex;
  align-items: center;
}
.info-label {
  font-weight: 500;
  margin-right: 3px;
}
.images-container {
  display: flex;
  flex: 1;
  overflow: auto;
  padding: 0;
  margin: 0;
}
.image-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.image-wrapper button {
  margin-top: 5px;
  line-height: 28px;
  padding: 0 8px;
}
.image-wrapper button mat-icon {
  height: 16px;
  width: 16px;
  font-size: 16px;
}
.image-title {
  text-align: center;
  margin: 10px 0;
}
.image-box {
  flex: 1;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin: 0 10px;
  border-radius: 10px;
}
.image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
  padding: 10px;
}
.no-image mat-icon {
  margin-bottom: 5px;
}
@media (max-width: 767px) {
  .images-container {
    flex-direction: column;
  }
  .image-wrapper {
    width: 100%;
    margin-bottom: 5px;
  }
  .defect-info {
    flex-direction: column;
    align-items: flex-start;
  }
  .info-item {
    margin: 2px 0;
  }
}
