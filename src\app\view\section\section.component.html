<mat-sidenav-container class="layout-container">
  <navbar [isMobileMenuOpen]="isMobileSidebarOpen" (toggleSidebar)="onToggleSidebar()"></navbar>
  <mat-sidenav #sidenav [opened]="!isMobile" [mode]="sidenavMode" class="sidenav" *ngIf="!isMobile">
    <sidebar></sidebar>
  </mat-sidenav>
  <div class="ebar-wrsidebar-wrapper" [class.active]="isMobileSidebarOpen" *ngIf="isMobile">
    <div
      class="mobile-overlay"
      [class.active]="isMobileSidebarOpen"
      (click)="closeMobileSidebar()"
      *ngIf="isMobileSidebarOpen"></div>
    <div class="mobile-sidebar" [class.open]="isMobileSidebarOpen">
      <sidebar [isMobileOpen]="isMobileSidebarOpen" (closeMobile)="closeMobileSidebar()"></sidebar>
    </div>
  </div>
  <mat-sidenav-content class="content-area"><router-outlet></router-outlet></mat-sidenav-content>
</mat-sidenav-container>
