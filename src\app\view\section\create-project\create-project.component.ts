import { Component } from '@angular/core';
// import { City, Country, State } from 'country-state-city';

@Component({
  selector: 'app-create-project',
  templateUrl: './create-project.component.html',
  styleUrls: ['./create-project.component.css'],
})
export class CreateProjectComponent {
  countries: any;
  cities: any;
  states: any;

  // ngOnInit(): void {
  // this.countries = Country.getAllCountries();
  // this.states = State.getAllStates();
  // this.cities = City.getAllCities();
  // }
}
