.slideshow-container {
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 700px;
  overflow-y: auto;
  background: white;
}
.slide {
  display: none;
  padding: 20px;
  text-align: center;
}
.slide.active {
  display: block;
}
.slide img {
  max-width: 90%;
  height: auto;
  max-height: 700px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 10px;
}
.prev,
.next {
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding: 16px;
  color: white;
  font-weight: bold;
  font-size: 18px;
  border-radius: 3px;
  user-select: none;
  background-color: var(--primary);
  text-decoration: none;
}
.next {
  right: 20px;
}
.prev {
  left: 20px;
}
.prev:hover,
.next:hover {
  background-color: rgba(0, 0, 0, 0.4);
}
.slide-number {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--primary);
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
}
.title {
  text-align: center;
  color: #333;
  margin-top: 20px;
  font-size: 30px;
  font-weight: bold;
}
.slide-title {
  margin: 10px 0;
  font-size: 20px;
  color: var(--primary);
  font-weight: bold;
}
.description {
  margin: 15px auto;
  font-size: 16px;
  color: #666;
  max-width: 800px;
  line-height: 1.5;
}
.fullscreen-btn {
  position: absolute;
  top: 10px;
  right: 16px;
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
}
.fullscreen-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}
.close-btn {
  background-color: var(--primary) !important;
  left: 0.625rem;
  position: absolute;
  color: var(--white);
  z-index: 9999 !important;
  top: 0.625rem;
}
.nav-hint {
  text-align: center;
  color: #666;
  margin-top: 10px;
  font-size: 14px;
  font-style: italic;
}
.dots {
  text-align: center;
  padding: 10px;
}
.dot {
  cursor: pointer;
  height: 12px;
  width: 12px;
  margin: 0 4px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
  transition: background-color 0.3s ease;
}
.actives {
  background-color: #4caf50;
}
.download-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
}
.download-btn:hover {
  background-color: #0056b3;
}
