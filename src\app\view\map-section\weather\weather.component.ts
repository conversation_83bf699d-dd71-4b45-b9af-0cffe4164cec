import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { Component, Input, OnInit } from '@angular/core';
import { Country } from 'country-state-city';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-weather',
  templateUrl: './weather.component.html',
  styleUrls: ['./weather.component.css'],
})
export class WeatherComponent implements OnInit {
  @Input() lat: number = 0;
  @Input() lon: number = 0;
  unit: string = 'C';
  weatherData: any = null;
  myObject: any = null;
  weather_img: string = environment.weather;

  constructor(
    private apiConfigService: ApiConfigService,
    private sharedDataService: SharedDataService,
    private toastr: ToastrService
  ) {}

  ngOnInit() {
    this.sharedDataService
      .fetchData(
        this.apiConfigService.getWeather(this.lat, this.lon),
        `weather_${this.lat + this.lon}`
      )
      .subscribe(
        (data: any) => {
          if (data) {
            const countryCode = data.sys?.country;
            this.weatherData = {
              country: Country.getCountryByCode(countryCode).name,
              city: data.name,
              temperatureC: Math.ceil(data.main?.temp),
              temperatureF: Math.ceil((Math.ceil(data.main?.temp) * 9) / 5 + 32),
              date: this.formatDate(new Date(data.dt * 1000)),
              time: this.formatTime(new Date(data.dt * 1000)),
            };
            this.myObject = {
              'prec in 24hrs': (data.rain?.['1h'] || 0.0) + ' mm',
              humidity: data.main?.humidity + ' %',
              'wind speed': data.wind?.speed + ' m/s',
              'feels like': Math.ceil(data.main?.feels_like) + ' °C',
            };
          }
        },
        err => {
          this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
          console.error(err);
        }
      );
  }
  toggleUnit() {
    this.unit = this.unit === 'C' ? 'F' : 'C';
  }
  formatDate(date: Date): string {
    const day = date.getDate();
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  }
  formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  }
}
