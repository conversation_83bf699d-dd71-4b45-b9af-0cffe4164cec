<div class="sharecomponent">
  <p class="title mb-4">Users Access List - {{ data.projectName }}</p>
  <table mat-table [dataSource]="dataSource" matSort>
    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Full Name</th>
      <td mat-cell *matCellDef="let user" class="text-capitalize">{{ user.name }}</td>
    </ng-container>
    <ng-container matColumnDef="email">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
      <td mat-cell *matCellDef="let user" class="text-lowercase">{{ user.email }}</td>
    </ng-container>
    <ng-container matColumnDef="role">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Role</th>
      <td mat-cell *matCellDef="let user" class="text-capitalize">
        {{ user.role }}
      </td>
    </ng-container>
    <ng-container matColumnDef="access">
      <th mat-header-cell *matHeaderCellDef>Access</th>
      <td mat-cell *matCellDef="let user">
        <mat-slide-toggle [(ngModel)]="user.access" (change)="updateRole(user)"></mat-slide-toggle>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <mat-paginator
    [pageSize]="5"
    [pageSizeOptions]="[5, 10, 20]"
    showFirstLastButtons></mat-paginator>
  <mat-dialog-actions class="justify-content-center">
    <button mat-button class="w-25 text-uppercase" (click)="onClose()" [disabled]="Isworking">
      <span *ngIf="!Isworking">Close</span>
      <span *ngIf="Isworking">
        <div class="spinner-border" role="status" *ngIf="Isworking"></div>
      </span>
    </button>
  </mat-dialog-actions>
</div>
