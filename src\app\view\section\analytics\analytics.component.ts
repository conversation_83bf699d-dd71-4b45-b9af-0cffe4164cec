import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { HttpService } from '@/view/map-section/services-map/http.service';
import { Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';

@Component({
  selector: 'app-analytics',
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.css'],
})
export class AnalyticsComponent implements OnInit, OnDestroy {
  @ViewChild('energy') energyInputRef: ElementRef;
  @ViewChild('revenue') revenueInputRef: ElementRef;
  project_id_summary: any;
  date_summary: any;
  Project_layer_summary: any;
  Project_layer_summary_values: any;
  power_loss_values: any;
  power_loss_values_energy: any = '';
  power_loss_values_revenue: any = '';
  data: any;
  project_layer_summary_data = [];
  project_layer_summary_lable = [];
  Hotspot_inv_val = [];
  ShortCircit_inv_val = [];
  Open_Circuit_inv_val = [];
  pannel_inv_val = [];
  Pid_val = [];
  total_count = [];
  power_loss_keys = [];
  samp = [];
  main_data: any;
  bardata = [];
  inv_main_data: any;
  datevalue: any;
  project_id_inv: any;
  date_inv: any;
  Project_layer_inv: any;
  Project_layer_inverter_data: any;
  Project_layer_inverter_data_values: any;
  project_layer_inv_data = [];
  project_layer_inv_lable = [];
  Hotspot_inv = [];
  ShortCircit_inv = [];
  Open_Circuit_inv = [];
  pannel_inv = [];
  Pid = [];
  total_count_inverter = [];
  selector: any;
  date: any;
  summary_def_def: any;
  summary_def_cou: any;
  inverter_def_def: any;
  inverter_def_cou: any;
  ana_def_def: any;
  ana_def_cou: any;
  hotpot_summary: any;
  short_circuit_summary: any;
  Open_circuit_summary: any;
  Panel_Failure_summary: any;
  PID_summary: any;
  SelectedDate: any;
  getting_date: any;
  inver_wise_data = [];
  inver_wise_lable = [];
  inver_wise_def_lable: any;
  inver_wise_def_hot: any;
  inver_wise_def_sc: any;
  inver_wise_def_os: any;
  inver_wise_def_pf: any;
  inver_wise_def_pid: any;
  currentopenedtab = [];
  currenttabcount: any;
  firstvalue: any;
  newDatefromAllProjects: any;
  date_summary_status_key: any;
  date_summary_status_value: any;
  Completed_date_array = [];
  summary_color: any;
  dashboard_total_key: string[];
  dashboard_total_values: unknown[];
  total_defects: any;
  plant_size_scanned: any;
  total_power_loss: any;
  project_layer_inverter_check: any;
  health_history: any = [];
  health_history_data: any = [];
  health_history_keys: any;
  total_power_loss_dates: any;
  total_no_defects_dates: any;
  defects_dates: any;
  powerloss_keys: any;
  plant_capacity_datewise: any = 0;
  total_power_loss_datewise: any = 0;
  total_no_defects_datewise: any = 0;
  Total_power_loss_percenntage: any;
  current_year: any;
  project_name: any;
  ana_def_cou_perc_1: any;
  ana_def_cou_desc_1: any;
  ana_def_cou_desc: any;
  ana_desc: string[] = [
    'Power dissipation occurring in a small area results in cell overheating',
    'One or more substring Open circuit failure with hotspot. At one or more substrings, easily mistaken for cell breakage or cell defects, Potential induced degradation (PID) or mismatch',
    'Loss of connection within module junction box or cell connector',
    'Frames of the modules are homogeneously heated. The negative grounding to be checked at inverter level. The module frames would have high leakage current',
    'The full panel surface is homogeneously heated up compared to other panels. It may happen due to PID effects.',
  ];
  energy: any;
  total_energy_loss: any;
  tariff: any;
  total_tariff: any;
  health_history_key_data: any;
  total_module_count: any;
  module_count_defectwise_key: any;
  module_count_defectwise_value: any;
  DC_Capacity_in_KW: any;
  Total_power_loss_in_ppm: any;
  total_energy_loss_in_ppm: any;
  total_energy_loss_in_KW_ppm: any;
  total_energy_loss_calc_in_ppm: any;
  energy_defectwise: string[] = [
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
  ];
  revenue_defectwise: string[] = [
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
    '0',
  ];
  total_revenue_loss_in_ppm: any;
  total_revenue_loss_in_KW_ppm: any;
  total_revenue_loss_in_ppm_INR: any;
  Chart_hide_show = 'hide';
  Chart_hide_show_revenue = 'hide';
  anamolies_style: any;
  inverter_style: any;
  health_style: any;
  powerloss_style: any;
  graph_anamoly_bar: any = {};
  graph_anamoly_pie: any = {};
  graph_health_bar: any = {};
  graph_health_pie: any = {};
  graph_inverter_bar: any = {};
  graph_inverter_bar_2: any = {};
  graph_power_pie: any = {};
  graph_power_donut_pie: any = {};
  graph_ppm_bar: any = {};
  graph_losses_pie: any = {};
  graph_losses_pie_2: any = {};
  da2: any[];
  Project_layer_summary2: any[];
  label_inverter: any[];
  data_anamoly_pie: any[];
  data_anamoly_lab: any[];
  data_anamoly_desc: any[];
  data_perc_for_anam;
  data_powerloss_key;
  data_powerloss_data;
  inverterData: any;
  defectImages: any = {
    hotspot: '../../../../assets/images/defects/Hotspot.png',
    shortcircuit: '../../../../assets/images/defects/Short_Circuit.png',
    opencircuit: '../../../../assets/images/defects/Open_circuit.png',
    panelfailure: '../../../../assets/images/defects/Panel_Failure.png',
    pid: '../../../../assets/images/defects/PID.png',
  };
  statsData: any = {};
  defectDetails: any = {};
  inverterCount: any = [];
  isUserInitiatedChange = false;
  energyValue: string = '';
  revenueValue: string = '';

  constructor(
    private _http: HttpService,
    private apiConfigService: ApiConfigService,
    private sharedDataService: SharedDataService
  ) {}

  ngOnInit(): void {
    this._http.set_analytics_icon(true);
    const project_id = localStorage.getItem('project_id');
    this.sharedDataService
      .fetchData(this.apiConfigService.getProjectData(project_id), `project_id_${project_id}`)
      .subscribe((data: any) => {
        this.main_data = data;
        this.project_id_summary = Object.keys(this.main_data);
        this.date_summary_status_key = Object.keys(this.main_data['date_status']);
        this.date_summary_status_value = Object.values(this.main_data['date_status']);
        this.Completed_date_array = this.date_summary_status_key.filter(
          (key, index) => this.date_summary_status_value[index] === 'completed'
        );
        const lastIndex = this.Completed_date_array.length - 1;
        this.datevalue = localStorage.getItem('date') || this.Completed_date_array[lastIndex];
        this.selectChange(this.datevalue);
        this.pow();
      });
  }

  onUserDateChange(): void {
    this.isUserInitiatedChange = true;
    this.selectChange(this.datevalue);
  }

  selectChange(selectedDate: string): void {
    localStorage.setItem('date', selectedDate);
    if (this.isUserInitiatedChange) {
      window.location.reload();
    }
    this.date = selectedDate;
    this.current_year = selectedDate;
    this.datevalue = selectedDate;
    this.project_layer_summary_data = [];
    this.project_layer_summary_lable = [];
    this.Hotspot_inv_val = [];
    this.ShortCircit_inv_val = [];
    this.Open_Circuit_inv_val = [];
    this.pannel_inv_val = [];
    this.Pid_val = [];
    this.total_count = [];
    this.power_loss_keys = [];
    const layer_data = this.main_data['processed_data'][selectedDate];
    this.Project_layer_summary = Object.keys(layer_data['summary_layers']);
    this.Project_layer_summary_values = Object.values(layer_data['summary_layers']);
    this.Project_layer_inverter_data = Object.keys(layer_data['inverter_layers']);
    this.Project_layer_inverter_data_values = Object.values(layer_data['inverter_layers']);
    this.inverterData = Object.keys(this.Project_layer_inverter_data_values[0]);
    this.health_history = Object.values(layer_data['health_history']);
    this.health_history_key_data = Object.keys(layer_data['health_history']);
    if (Object.keys(layer_data['power_loss'])) {
      this.power_loss_keys = Object.keys(layer_data['power_loss']);
      this.power_loss_values = Object.values(layer_data['power_loss']);
      this.power_loss_values_energy = Object.values(layer_data['power_loss']);
      this.power_loss_values_revenue = Object.values(layer_data['power_loss']);
    }
    this.plant_capacity_datewise = layer_data['plant_size_scanned'];
    this.total_power_loss_datewise = layer_data['total_power_loss'];
    this.total_no_defects_datewise = layer_data['total_no_defects'];
    this.statsData = [
      {
        title: 'Total capacity scanned',
        value: this.plant_capacity_datewise,
        unit: 'MW',
        subtitle: 'Inspected till date',
        icon: 'widgets',
      },
      {
        title: 'Net Power Loss',
        value: this.total_power_loss_datewise,
        subtitle: 'Estimated Power Loss',
        unit: 'kW',
        icon: 'flash_on',
      },
      {
        title: 'No. of Defects',
        value: this.total_no_defects_datewise,
        subtitle: 'Defects detected',
        unit: '',
        icon: 'device_hub',
      },
    ];
    this.total_module_count = layer_data['total_modules_present'];
    this.powerloss_style = this.power_loss_keys.length === 0 ? 'hidden' : 'show';
    this.current_year = selectedDate;
    this.health_history_data = [];
    this.total_power_loss_dates = [];
    this.total_no_defects_dates = [];
    this.defects_dates = [];
    let Total_power_loss = 0;
    this.Total_power_loss_percenntage = [];
    this.module_count_defectwise_key = [];
    this.module_count_defectwise_value = [];

    for (let v = 0; v < this.health_history_key_data.length; v++) {
      const modulecount_defectwise = (
        (this.health_history[v] / this.total_module_count) *
        1000000
      ).toFixed(2);
      this.module_count_defectwise_value.push(modulecount_defectwise);
    }

    for (let p = 0; p < this.date_summary_status_key.length; p++) {
      const d = new Date(this.current_year);
      const array_date = new Date(this.date_summary_status_key[p]);
      if (array_date.getFullYear() <= d.getFullYear()) {
        const processedData = this.main_data['processed_data'][this.date_summary_status_key[p]];
        this.health_history = Object.values(processedData['health_history']);
        this.total_power_loss_dates.push(processedData['total_power_loss']);

        const new_defects = processedData['total_no_defects'];
        if (new_defects !== 0) {
          this.total_no_defects_dates.push(new_defects);
          this.defects_dates.push([this.date_summary_status_key[p]]);
        }

        this.health_history_data.push({
          name: this.date_summary_status_key[p],
          data: this.health_history,
        });
      }
    }

    this.health_history_keys = Object.keys(layer_data['health_history']);
    this.summary_color = [];

    for (let i = 0; i < this.Project_layer_summary.length; i++) {
      this.project_layer_summary_data.push(this.Project_layer_summary_values[i]['Count']);
      this.project_layer_summary_lable.push(this.Project_layer_summary[i]);
    }

    this.ana_def_def = this.project_layer_summary_lable[0];
    this.ana_def_cou = this.project_layer_summary_data[0];

    for (let i = 0; i < this.Project_layer_inverter_data_values.length; i++) {
      this.Hotspot_inv_val.push(
        this.Project_layer_inverter_data_values[i][this.inverterData[0]]['count']
      );
      this.ShortCircit_inv_val.push(
        this.Project_layer_inverter_data_values[i][this.inverterData[1]]['count']
      );
      this.Open_Circuit_inv_val.push(
        this.Project_layer_inverter_data_values[i][this.inverterData[2]]['count']
      );
      this.pannel_inv_val.push(
        this.Project_layer_inverter_data_values[i][this.inverterData[3]]['count']
      );
      this.Pid_val.push(this.Project_layer_inverter_data_values[i][this.inverterData[4]]['count']);

      this.inver_wise_lable.push(this.Project_layer_inverter_data[i]);
    }

    for (let n = 0; n < this.Project_layer_inverter_data.length; n++) {
      this.total_count.push(
        +this.Hotspot_inv_val[n] +
          +this.ShortCircit_inv_val[n] +
          +this.Open_Circuit_inv_val[n] +
          +this.pannel_inv_val[n] +
          +this.Pid_val[n]
      );
    }

    for (let z = 0; z < this.power_loss_keys.length; z++) {
      Total_power_loss += this.power_loss_values[z];
    }

    this.data_powerloss_key = [];
    this.data_powerloss_data = [];

    for (let z = 0; z < this.power_loss_keys.length; z++) {
      const percentage_calc = this.power_loss_values[z] / Total_power_loss;
      const n_percentage_calc = parseFloat(
        (Math.round(this.total_power_loss_datewise * percentage_calc * 100) / 100).toFixed(2)
      );
      this.Total_power_loss_percenntage.push(n_percentage_calc);
      this.data_powerloss_key.push(this.power_loss_keys[z]);
      this.data_powerloss_data.push(this.power_loss_values[z]);
    }
    this.inver_wise_def_lable = this.inver_wise_lable[0];

    const defectCounts = [];
    const dataval = this.Project_layer_inverter_data_values[0];
    defectCounts.push({
      key: 'Inverter Name',
      value: this.inver_wise_def_lable,
    });

    for (const defect in dataval) {
      defectCounts.push({
        key: defect,
        value: dataval[defect].count,
      });
      defectCounts[defect] = dataval[defect].count;
    }
    this.defectDetails = defectCounts;
    this.inverterCount.push(
      {
        key: 'Inverter Name',
        value: this.Project_layer_inverter_data[0],
      },
      {
        key: 'Defect Count',
        value: this.total_count[0],
      }
    );
  }

  updateInnerHTML(elementId: string, value: string) {
    const element = document.getElementById(elementId);
    if (element) {
      element.innerHTML = `${value}`;
      element.style.transition = 'background-color 0.3s';
      element.style.backgroundColor = 'rgba(0, 139, 139, 0.1)';
      setTimeout(() => {
        element.style.backgroundColor = 'transparent';
      }, 500);
    }
  }

  updateImage(elementId: string, src: string) {
    const element = document.getElementById(elementId);
    if (element) {
      element.innerHTML = `<img src='${src}' width="100%" style="border-top-left-radius: 10px; border-top-right-radius: 10px;">`;
      element.style.transition = 'transform 0.3s';
      element.style.transform = 'scale(1.02)';
      setTimeout(() => {
        element.style.transform = 'scale(1)';
      }, 300);
    }
  }

  onClick(eventData: any) {
    this.inverter_style = this.Project_layer_inverter_data_values.length !== 0 ? 'show' : 'hidden';

    if (this.Project_layer_summary2.length === 0) return;

    for (let i = 0; i < eventData.points.length; i++) {
      const selector = eventData.points[i].pointNumber;
      this.updateInnerHTML(this.sanitizeClassName('itc'), `<b>${eventData.points[i].label}</b>`);
      const element = document.getElementById(
        this.sanitizeClassName(eventData.points[i].data.name)
      );
      if (element) {
        element.innerHTML = `<p>${this.da2[selector][eventData.points[i].data.name]['count']}</p>`;
      }
    }
  }

  sanitizeClassName(key: string): string {
    return key.replace(/\s+/g, '-').toLowerCase();
  }

  onClickInverterBar2(eventData: any) {
    for (let i = 0; i < eventData.points.length; i++) {
      const elements = document.getElementsByClassName('countValue');
      elements[0].innerHTML = `<p>${eventData.points[i].label}</p>`;
      elements[1].innerHTML = `<p>${eventData.points[i].value}</p>`;
    }
  }

  onClickanamolyBar(eventData: any) {
    for (let i = 0; i < eventData.points.length; i++) {
      const selector = eventData.points[i].pointNumber;
      const key = this.data_anamoly_lab[selector].replace(/_/g, '').replace(/ /g, '').toLowerCase();

      this.updateInnerHTML('ana_summary_def', this.data_anamoly_lab[selector]);
      this.updateInnerHTML('ana_summary_cou', this.data_anamoly_pie[selector]);
      this.updateImage('ana_summary_im', this.defectImages[key]);
    }
  }

  onClickanamolyPie(eventData: any) {
    for (let i = 0; i < eventData.points.length; i++) {
      const selector = eventData.points[i].pointNumber;
      const key = this.data_anamoly_lab[selector].replace(/_/g, '').replace(/ /g, '').toLowerCase();

      this.updateInnerHTML('ana_summary_def_1', this.data_anamoly_lab[selector]);
      this.updateInnerHTML('ana_summary_cou_1', this.data_anamoly_pie[selector]);
      this.updateInnerHTML(
        'ana_summary_cou_perc_1',
        ((this.data_anamoly_pie[selector] / this.data_perc_for_anam) * 100).toFixed(1) + ' %'
      );
      this.updateInnerHTML('ana_summary_cou_desc_1', this.data_anamoly_desc[selector]);
      this.updateImage('ana_summary_im_1', this.defectImages[key]);
    }
  }

  onClickPowerPie(eventData: any) {
    if (this.data_powerloss_key.length === 0) return;

    for (let i = 0; i < eventData.points.length; i++) {
      const selector = eventData.points[i].pointNumber;
      const key = this.data_powerloss_key[selector]
        .replace(/_/g, '')
        .replace(/ /g, '')
        .toLowerCase();
      this.updateInnerHTML('summary_def', this.data_powerloss_key[selector]);
      this.updateInnerHTML('summary_cou', (this.data_powerloss_data[selector] / 1000).toFixed(1));
      this.updateImage('summary_im', this.defectImages[key]);
    }
  }

  ngOnDestroy() {
    this._http.set_analytics_icon(false);
  }

  tabClick(tab) {
    let sel = tab === 'Power Loss Metrics' && this.datevalue ? tab : tab['tab']['textLabel'];
    if (sel !== 'Power Loss Metrics') {
      this.currentopenedtab.push(sel);
      this.currenttabcount = this.currentopenedtab.length;
      sel = this.currentopenedtab[this.currenttabcount - 1];
    }
    const tabActions = {
      'Power Loss Metrics': () => this.pow(),
      'Anomaly Classification': () => {
        this.ana(this.project_layer_summary_data, this.project_layer_summary_lable, this.ana_desc);
      },
      'Inverter-Wise Analysis': () =>
        this.inver(
          this.Project_layer_inverter_data_values,
          this.inver_wise_lable,
          this.Project_layer_summary
        ),
      'Health History': () => this.helth(),
      Losses: () => this.losses(),
      'Parts per million': () => this.partspermil(),
    };
    if (tabActions[sel]) tabActions[sel]();
  }

  plotChart(chartType, xData, yData, labels, title, layoutOptions = {}, configOptions = {}) {
    return {
      data: [
        {
          x: xData,
          y: yData,
          values: yData,
          labels: labels,
          type: chartType,
          mode: chartType === 'scatter' ? 'lines+markers' : undefined,
          textposition: 'inside',
        },
      ],
      layout: {
        title: { text: title, automargin: true, xref: 'paper' },
        showLegend: true,
        autosize: true,
        margin: { l: 30, r: 30, b: 30, t: 50 },
        height: 400,
        ...layoutOptions,
      },
      config: {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        scrollZoom: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
        ...configOptions,
      },
    };
  }

  createPowerLossChart({
    powerLossValues,
    powerLossKeys,
    titleText,
    formattedPowerLossValues = [],
  }) {
    const formattedValues = formattedPowerLossValues.length
      ? formattedPowerLossValues
      : powerLossValues.map(value => {
          const total = powerLossValues.reduce((acc, curr) => acc + curr, 0);
          const percent = ((value / total) * 100).toFixed(1);
          return `${percent}%`;
        });

    return {
      data: [
        {
          values: powerLossValues,
          labels: powerLossKeys,
          type: 'pie',
          showlegend: false,
          hoverinfo: 'label+percent',
          textinfo: 'label+text',
          text: formattedValues,
          textposition: 'inside',
        },
      ],
      layout: {
        title: {
          text: titleText,
          xref: 'paper',
        },
        autosize: true,
        margin: { l: 30, r: 30, b: 30, t: 50 },
        height: 400,
      },
      config: {
        responsive: true,
        scrollZoom: false,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
      },
    };
  }

  losses() {
    this.power_loss_values_energy = Object.values(
      this.main_data['processed_data'][this.datevalue]['power_loss']
    );
    this.DC_Capacity_in_KW = this.plant_capacity_datewise * 1000;
    this.Total_power_loss_in_ppm = (
      (this.total_power_loss_datewise / this.DC_Capacity_in_KW) *
      1000000
    ).toFixed(2);
  }

  partspermil() {
    this.module_count_defectwise_value = this.health_history.map(v =>
      ((v / this.total_module_count) * 1000000).toFixed(2)
    );
    this.graph_ppm_bar = this.plotChart(
      'scatter',
      this.health_history_key_data,
      this.module_count_defectwise_value,
      this.health_history_key_data,
      'Defect Categories vs Parts per Million (PPM)'
    );
  }

  pow() {
    if (this.power_loss_values.length === 0) return;
    this.powerloss_style = this.power_loss_keys.length === 0 ? 'hidden' : 'show';
    this.summary_def_def = this.power_loss_keys[0];
    this.summary_def_cou = (this.power_loss_values[0] / 1000).toFixed(1);

    this.graph_power_pie = this.createPowerLossChart({
      powerLossValues: this.power_loss_values,
      powerLossKeys: this.power_loss_keys,
      titleText: '% Contribution at Defect level',
      formattedPowerLossValues: [],
    });

    this.graph_power_donut_pie = this.createPowerLossChart({
      powerLossValues: this.power_loss_values,
      powerLossKeys: this.power_loss_keys,
      titleText: '% Contribution at Defect level',
      formattedPowerLossValues: this.power_loss_values.map(
        value => (value / 1000).toFixed(1) + ' kW'
      ),
    });
  }

  helth() {
    this.firstvalue = 'n';
    this.health_style =
      this.Completed_date_array.length !== 0 && this.health_history.length !== 0
        ? 'show'
        : 'hidden';

    this.graph_health_pie = this.plotChart(
      'pie',
      this.defects_dates,
      this.total_no_defects_dates,
      this.defects_dates,
      'Date Distribution Overview'
    );

    const health_history_keys_2 = this.health_history_data
      .filter(data => data.length !== 0)
      .map(data => ({
        type: 'scatter',
        mode: 'lines+markers',
        name: data.name,
        x: this.health_history_keys,
        y: data.data,
      }));

    this.graph_health_bar = {
      data: health_history_keys_2,
      layout: {
        title: {
          text: 'Defect Changes with Date',
          automargin: true,
          xref: 'paper',
        },
        showLegend: true,
        autosize: true,
        margin: { l: 10, r: 10, t: 20 },
        height: 400,
      },
      config: {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
      },
    };
  }

  inver(da, lable, Project_layer_summary) {
    this.da2 = da;
    this.label_inverter = lable;
    this.Project_layer_summary2 = Project_layer_summary;
    this.firstvalue = 'n';

    if (this.Project_layer_inverter_data_values.length !== 0) {
      this.inverter_style = 'show';
    } else {
      this.inverter_style = 'hidden';
    }

    this.graph_inverter_bar = {
      data: [
        {
          x: this.Project_layer_inverter_data,
          y: this.Hotspot_inv_val,
          type: 'bar',
          name: this.inverterData[0],
        },
        {
          x: this.Project_layer_inverter_data,
          y: this.ShortCircit_inv_val,
          type: 'bar',
          name: this.inverterData[1],
        },
        {
          x: this.Project_layer_inverter_data,
          y: this.Open_Circuit_inv_val,
          type: 'bar',
          name: this.inverterData[2],
        },
        {
          x: this.Project_layer_inverter_data,
          y: this.pannel_inv_val,
          type: 'bar',
          name: this.inverterData[3],
        },
        {
          x: this.Project_layer_inverter_data,
          y: this.Pid_val,
          type: 'bar',
          name: this.inverterData[4],
        },
      ],
      layout: {
        autosize: true,
        showlegend: true,
        title: {
          text: 'Inverter Defect Comparison',
          automargin: true,
          xref: 'paper',
        },
        height: 400,
        margin: { l: 30, r: 30, b: 30, t: 50 },
      },
      config: {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
      },
    };

    const inverterTraces = [];
    for (let n = 0; n < this.Project_layer_inverter_data.length; n++) {
      inverterTraces.push({
        x: [this.Project_layer_inverter_data[n]],
        y: [this.total_count[n]],
        name: 'Defect Count',
        type: 'bar',
      });
    }

    this.graph_inverter_bar_2 = this.plotChart(
      'bar',
      this.Project_layer_inverter_data,
      this.total_count,
      this.Project_layer_inverter_data,
      'Defect Counts per Inverter Unit'
    );
  }

  ana(dat, lab, desc) {
    this.data_anamoly_pie = dat;
    this.data_anamoly_lab = lab;
    this.data_anamoly_desc = desc;

    this.firstvalue = 'n';
    this.anamolies_style = this.project_layer_summary_data.length !== 0 ? 'show' : 'hidden';

    let perc_for_anam = 0;
    this.ana_def_cou_desc = [];
    for (let z = 0; z < this.project_layer_summary_lable.length; z++) {
      perc_for_anam += dat[z];
      this.data_perc_for_anam = perc_for_anam;
      this.ana_def_cou_desc.push([desc[z]]);
    }
    this.ana_def_cou_perc_1 = ((dat[0] / perc_for_anam) * 100).toFixed(1);
    this.ana_def_cou_desc_1 = this.ana_def_cou_desc[0];

    if (this.project_layer_summary_data.length > 0) {
      this.graph_anamoly_bar = this.plotChart('bar', lab, dat, lab, 'Defect Composition');

      this.graph_anamoly_pie = this.createPowerLossChart({
        powerLossValues: dat,
        powerLossKeys: lab,
        titleText: '% Contribution at Defect level',
        formattedPowerLossValues: dat.map(value => (value / 1000).toFixed(1) + ' kW'),
      });
    }
  }

  energyloss_calc() {
    const energy = parseFloat(this.energyValue);
    if (!energy || energy <= 0 || isNaN(energy)) {
      alert('Please enter a valid energy value greater than 0.');
      return;
    }
    this.Chart_hide_show = 'show';
    this.total_energy_loss = (this.total_power_loss_datewise * energy).toFixed(2);
    const totalLoss = this.power_loss_values.reduce((a, b) => a + b, 0);
    this.energy_defectwise = this.power_loss_values.map(value =>
      ((value / totalLoss) * parseFloat(this.total_energy_loss)).toFixed(2)
    );

    this.graph_losses_pie = {
      data: [
        {
          values: this.energy_defectwise,
          labels: this.power_loss_keys,
          type: 'pie',
          textposition: 'inside',
          hoverinfo: 'label+percent',
          textinfo: 'label+percent',
          insidetextorientation: 'radial',
        },
      ],
      layout: {
        title: {
          text: 'Energy Loss Distribution (kWh/year)',
        },
        autosize: true,
        margin: { l: 30, r: 30, b: 30, t: 50 },
        height: 400,
      },
      config: {
        responsive: true,
        scrollZoom: false,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
      },
    };
  }

  tariff_cal() {
    const revenue = parseFloat(this.revenueValue);
    if (!revenue || revenue <= 0 || isNaN(revenue)) {
      alert('Please enter a valid tariff value greater than 0.');
      return;
    }
    this.Chart_hide_show_revenue = 'show';
    this.total_tariff = (parseFloat(this.total_energy_loss) * revenue).toFixed(2);
    this.revenue_defectwise = this.energy_defectwise.map(val =>
      (parseFloat(val) * revenue).toFixed(2)
    );
    this.graph_losses_pie_2 = {
      data: [
        {
          values: this.revenue_defectwise,
          labels: this.power_loss_keys,
          type: 'pie',
          textposition: 'inside',
          hoverinfo: 'label+percent+value',
          textinfo: 'label+value',
          insidetextorientation: 'horizontal',
        },
      ],
      layout: {
        title: {
          text: 'Revenue Loss Distribution (INR/Year)',
        },
        autosize: true,
        margin: { l: 30, r: 30, b: 30, t: 50 },
        height: 400,
      },
      config: {
        responsive: true,
        scrollZoom: false,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
      },
    };
  }
}
