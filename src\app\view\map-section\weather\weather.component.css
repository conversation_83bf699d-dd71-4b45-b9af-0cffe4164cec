#weather {
  min-width: 300px;
  border: solid 2px var(--primary);
  transition: display 0.3s ease-in-out;
  padding: 5px;
  background-color: white;
  border-radius: 10px;
}
#header {
  text-align: left;
  font-size: 16px;
  padding: 5px;
  font-weight: 600;
  background-color: var(--primary);
  color: white;
  border-radius: 5px;
}
#header2 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
#img-temp {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
#temp-unit {
  font-size: 20px;
  position: absolute;
  padding-top: 10px;
  vertical-align: top;
}
#header4 {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
#header4 #header4-part {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: solid 1px gray;
  padding: 4px;
}
#header4 #header4-part .value1 {
  font-size: 13px;
  font-weight: bold;
}
.tooltipweather .tooltiptext {
  visibility: hidden;
  width: 153px;
  background-color: whitesmoke;
  border: solid 2px white;
  color: var(--dark);
  text-align: center;
  border-radius: 5px;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  bottom: 100%;
  left: 50%;
  margin-left: -100px;
  font-size: 14px;
  font-weight: 600;
}
.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}
@media (max-width: 767px) {
  #weather {
    min-width: 280px;
    max-width: 95vw;
    padding: var(--spacing-sm);
    font-size: var(--font-sm);
  }
  #header {
    font-size: var(--font-sm);
    padding: var(--spacing-sm);
  }
  #header2 {
    font-size: var(--font-xs);
  }
  #img-temp {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  #temp-unit {
    font-size: var(--font-md);
    position: relative;
    padding-top: 0;
  }
  #header4 {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
  #header4 #header4-part {
    min-width: 60px;
    border-right: none;
    border-bottom: solid 1px gray;
    padding: var(--spacing-xs);
  }
  #header4 #header4-part:last-child {
    border-bottom: none;
  }
  #header4 #header4-part .value1 {
    font-size: var(--font-xs);
  }
  .tooltipweather .tooltiptext {
    width: 120px;
    margin-left: -60px;
    font-size: var(--font-xs);
  }
}
