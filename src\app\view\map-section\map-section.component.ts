import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { Drawtool, DrawtoolC } from '@/interfaces/drawtool';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, ElementRef, HostListener, OnInit, Renderer2, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import '@optometristpritam/leaflet-height/leaflet-elevation/src/index.js';
import 'leaflet';
import 'leaflet-draw';
import 'leaflet-kml';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import 'src/assets/plugins/leaflet.edgebuffer.js';
import 'src/assets/plugins/leaflet.history.js';
import { environment } from 'src/environments/environment';
import { HttpService } from './services-map/http.service';
import { SidebarComponent } from './sidebar/sidebar.component';
import { AoiDialogComponent } from './sub-components/aoi-dialog/aoi-dialog.component';
declare const L: any;
interface SummaryLayers {
  [key: string]: {
    defect_type: string;
    kml: string;
    color: string;
    sub_group?: Record<string, { criticality: string; kml: string; color: string }>;
  };
}
@Component({
  selector: 'app-map-section',
  templateUrl: './map-section.component.html',
  styleUrls: ['./map-section.component.css'],
})
export class MapSectionComponent implements OnInit {
  @ViewChild(SidebarComponent) sidebarComponent!: SidebarComponent;
  @ViewChild('mapContainer', { static: false }) mapContainer!: ElementRef;
  map!: any;
  domParser = new DOMParser();
  logo: string = '';
  currentTime: string = '';
  terrain_tile_layer: string = environment.terrain_tile_layer;
  sattelite_tile_layer: string = environment.sattelite_tile_layer;
  weather_show: boolean = false;
  storedDateValue: boolean;
  satellite_layer;
  popupDesc = null;
  track: any = null;
  allDefects = [];
  gb_layer: any = null;
  popupKml: any;
  popup_card_visibility: boolean = false;
  popup_card_visibility_cadestral: boolean = false;
  popup_card_visibility_grading: boolean = false;
  summaryState: string;
  removekml_list = [];
  removekml_list_inv = [];
  Project_data: any;
  Project_data_thermo: any;
  Project_data_grad: any;
  Project_data_values: any;
  Project_data_inverter_values: any;
  project_name: any;
  Project_data_inverter: any;
  ortho_file_location: any;
  base_ortho_layer: any;
  date: any;
  summ: any;
  center: any;
  Completed_date_array: any = [];
  lat: any;
  long: any;
  kml_file_location: any;
  polies = [];
  descObj: any;
  descObj_cadestral: any;
  table_no: any;
  thermal_location: any;
  cad_file_location: any;
  current_kml_data: any;
  Description_cadestral: any;
  Document: any;
  Survey_No: any;
  Document_link: any;
  popup_opened: boolean;
  satellite = '../../../assets/images/satellite.jpg';
  default = '../../../assets/images/default.jpg';
  DTM = '../../../assets/images/DroneData/DSM.png';
  Slope = '../../../assets/images/DroneData/Slope.png';
  Orthomosaic = '../../../assets/images/DroneData/Orthomosaic.png';
  Clear = '../../../assets/images/Clear.png';
  CAD = '../../../assets/images/DroneData/CAD.png';
  Thermal = '../../../assets/images/DroneData/Thermal.png';
  isShown1 = 'visibility_off';
  isShown2 = 'visibility_off';
  slope_layer: any;
  dtm_layer: any;
  zoom_level: number;
  subdefects_visibility = 'visibility_off';
  grading_visibility = 'visibility_off';
  defect_rectify_visibility = 'visibility_off';
  accepted: boolean;
  accepted1: boolean;
  accepted2: boolean;
  accepted3: boolean;
  accepted4: boolean;
  accepted5 = false;
  table_number: any;
  sidebarVisible = true;
  tablegendselect = false;
  popupContent: any = null;
  isModalOpen = false;
  isFullscreen = false;
  selectedImage: string = null;
  selectedImage2 = 'terrain';
  cad_val = false;
  project_id: number;
  opacity_value: any;
  userArray_value: any;
  userArray_Distance: any;
  csv_path: any;
  project_id_summary: any;
  minimum_value: number;
  maximum_value: number;
  slope: any;
  datevalue: any;
  graph_slope_bar: any = {};
  latitudes: any = [];
  longitudes: any = [];
  opacityStatus: boolean = false;
  rangeInput: HTMLInputElement;
  controlElevation: any = null;
  isOpacity: boolean = false;
  check: boolean = false;
  elevation: any;
  current_table_no: any;
  onceCalled: boolean = false;
  valuecheck: boolean = false;
  currentMenu: string = null;
  isOpen = true;
  projectInfo: { [key: string]: string } = {};
  drawnItems: any = new L.FeatureGroup();
  drawControl: any;
  markerItems: any;
  drawtool: Drawtool = new DrawtoolC();
  iconOptions = {
    iconUrl: '../../assets/images/marker.svg',
    iconSize: [30, 30],
  };

  constructor(
    private http: HttpClient,
    private _http: HttpService,
    private userService: UserService,
    private sharedDataService: SharedDataService,
    private apiConfigService: ApiConfigService,
    private toastr: ToastrService,
    public dialog: MatDialog,
    private renderer: Renderer2,
    private el: ElementRef
  ) {}

  ngOnInit(): void {
    this.logo = this.userService.getSidebarLogo();
    ['thermal', 'cad', 'DTM', 'slope', 'satellite', 'rawImage'].forEach(key =>
      localStorage.setItem(key, key)
    );
    this.setupResizeListener();
    this.loadProjectData();
  }
  @HostListener('window:resize')
  private setupResizeListener() {
    this.updateStyleOnResize();
  }
  private updateStyleOnResize() {
    const w = window.innerWidth;
    const tool = '.tool-sidebar',
      exam = '.exam2',
      toggle = '.toggle-right';
    const adjust = (sel: string, prop: string, val: string) => {
      const el = this.el.nativeElement.querySelector(sel);
      if (!el) return;
      if (w < 390) this.renderer.setStyle(el, prop, val);
      else this.renderer.removeStyle(el, prop);
    };
    adjust(tool, 'zIndex', '2000');
    adjust(exam, 'marginRight', '-2px');
    adjust(toggle, 'zIndex', '4000');
  }
  private loadProjectData() {
    const pid = localStorage.getItem('project_id');
    this.sharedDataService
      .fetchData(this.apiConfigService.getProjectData(pid), `project_id_${pid}`)
      .pipe(finalize(() => this.map_generate()))
      .subscribe(
        data => this.onProjectDataLoaded(data),
        err => {
          this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
          console.error(err);
        }
      );
  }
  private onProjectDataLoaded(data: any) {
    this.summ = data;
    this.project_id = data.id;
    this.project_name = data.name.toUpperCase().replace(/[_-]/g, ' ');
    this.center = data.center.split(',').map(s => s.trim()) as [string, string];
    [this.lat, this.long] = this.center;
    this.zoom_level = +data.zoom_level;

    this.date = localStorage.getItem('date') || data.dates[0];
    this.Completed_date_array = Object.entries(data.date_status)
      .filter(([, status]) => status === 'completed')
      .map(([date]) => date);

    const proc = data.processed_data[this.date];
    this.csv_path = proc.csv_path;
    this.collectDefects(proc.summary_layers);
    this.Project_data_thermo = Object.keys(proc.summary_layers);
    this.Project_data_grad = [
      ...Object.keys(proc.grading_layers),
      ...Object.keys(proc.topography_layers),
    ];

    this.ortho_file_location = proc.ortho_file_location;
    this.kml_file_location = proc.kml_file_location;
    this.thermal_location = proc.thermal_hotspot_location;
    this.cad_file_location = proc.cad_file_location;
  }
  private collectDefects(layers: SummaryLayers) {
    this.allDefects = [];
    Object.values(layers).forEach(layer => {
      if (layer.sub_group) {
        Object.values(layer.sub_group).forEach(sg =>
          this.allDefects.push({
            name: sg.criticality,
            kml: sg.kml,
            color: sg.color,
          })
        );
      } else {
        this.allDefects.push({
          name: layer.defect_type,
          kml: layer.kml,
          color: layer.color,
        });
      }
    });
  }
  async exportMap(): Promise<void> {
    try {
      this.toastr.warning('Preparing screenshot…', 'Capturing Screenshot');
      // this.currentTime = this.updateCurrentTime();
      // await this.preparePrintContainer();
      // window.print();
      document.querySelector('#print-map')!.innerHTML = '';
    } catch {
      this.toastr.error('Failed to capture screenshot', 'Error!!');
    }
  }
  private updateCurrentTime(): string {
    const now = new Date();
    const [d, m, y] = [now.getDate(), now.getMonth() + 1, now.getFullYear()].map(n =>
      String(n).padStart(2, '0')
    );
    let h = now.getHours();
    const ampm = h >= 12 ? 'PM' : 'AM';
    h = h % 12 || 12;
    const [min, sec] = [now.getMinutes(), now.getSeconds()].map(n => String(n).padStart(2, '0'));
    return `${d}-${m}-${y} ${String(h).padStart(2, '0')}:${min}:${sec} ${ampm}`;
  }
  private async preparePrintContainer() {
    const container = document.querySelector('#print-map')!;
    container.innerHTML = `<div class="print-map-content" style="width:100%;height:100%"></div>`;
    const clone = this.mapContainer.nativeElement.cloneNode(true);
    container.firstElementChild!.appendChild(clone);
    await new Promise(r => setTimeout(r, 500));
    (clone as any).mapObject?.invalidateSize();
  }
  handleRangeChange(e: Event) {
    this.opacity_value = parseFloat((e.target as HTMLInputElement).value);
    ['thermal_layer', 'dtm_layer', 'slope_layer'].forEach(l =>
      this[l]?.setOpacity(this.opacity_value)
    );
  }
  createDataForGpxfile(table_no: string): void {
    this.resetGpxData();
    this.table_number = table_no;
    this.grading_visibility = 'visible';
    const headers = new HttpHeaders().set('Range', 'bytes=0-').set('X-Skip-Authorization', 'true');
    const filePath = `${this.csv_path}${table_no}.csv`;
    this.http.get(filePath, { headers, responseType: 'text' }).subscribe(
      data => {
        const rows = data.trim().split('\n').slice(1); // skip header
        const coordinates = rows.map(row => {
          const [distance, lon, lat, value] = row.split(',').map(cell => cell.split('\r')[0]);
          const parsedDistance = parseFloat(parseFloat(distance).toFixed(3));
          const parsedLon = parseFloat(parseFloat(lon).toFixed(3));
          const parsedLat = parseFloat(parseFloat(lat).toFixed(3));
          const parsedValue = parseFloat(parseFloat(value).toFixed(3));
          this.userArray_Distance.push(parsedDistance);
          this.longitudes.push(parsedLon);
          this.latitudes.push(parsedLat);
          this.userArray_value.push(parsedValue);
          return [parsedLon, parsedLat, parsedValue];
        });

        this.minimum_value = Math.min(...this.userArray_value);
        this.maximum_value = Math.max(...this.userArray_value);
        const geoJSON = this.buildGeoJson(coordinates);
        this.elevation = URL.createObjectURL(
          new Blob([JSON.stringify(geoJSON)], { type: 'application/json' })
        );
        this.setupElevationControl();
      },
      err => {
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
        console.error(err);
      }
    );
  }
  private resetGpxData(): void {
    this.userArray_value = [];
    this.userArray_Distance = [];
    this.latitudes = [];
    this.longitudes = [];
  }
  private buildGeoJson(coords: number[][]) {
    return {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: coords,
          },
          properties: {
            name: 'MyLineString',
          },
        },
      ],
    };
  }
  private setupElevationControl(): void {
    const elevationOptions = {
      position: 'bottomright',
      theme: 'lime-theme',
      detached: false,
      collapsed: false,
      autohide: false,
      legend: true,
      downloadLink: false,
      almostOver: true,
      distanceMarkers: { distance: false, direction: true, lazy: true },
    };
    if (!this.check) {
      this.controlElevation = L.control.elevation(elevationOptions).addTo(this.map);
    } else {
      this.controlElevation.clear();
    }
    this.controlElevation.load(this.elevation);
    this.check = true;
  }
  openModal(flag: boolean) {
    this.isModalOpen = !flag;
  }
  closeStack() {
    this.isModalOpen = false;
  }
  map_generate() {
    const openstreetmap = L.tileLayer(this.terrain_tile_layer, {
      minZoom: 2,
      maxZoom: 23,
      edgeBufferTiles: 1,
    });
    this.base_ortho_layer = L.tileLayer(this.ortho_file_location + '{z}/{x}/{y}.png', {
      minZoom: 2,
      maxZoom: 23,
      edgeBufferTiles: 1,
    });
    this.map = L.map(this.mapContainer.nativeElement, {
      preferCanvas: true,
      attributionControl: false,
      zoomControl: false,
      minZoom: 2,
      maxZoom: 23,
      zoomSnap: 0.5,
      layers: [openstreetmap, this.base_ortho_layer],
      maxBoundsViscosity: 1.0,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    }).setView([0, 0], 2);

    L.control.scale().addTo(this.map);

    // new L.HistoryControl({
    //   backText: '<',
    //   forwardText: '>',
    // }).addTo(this.map);

    this.zoomreset();
    this.drawAOIPlugin();
  }
  Datemenu(date) {
    this._http.setclosesidebar({ close_side_bar: 'summarySidebar/True' });
    this.date = date;
    const summ_data = this.summ['processed_data'][date];
    localStorage.setItem('date', date);
    this.storedDateValue = true;
    this.Project_data = Object.keys(summ_data['summary_layers']);
    this.Project_data_values = summ_data['summary_layers'];
    this.Project_data_inverter = Object.keys(summ_data['inverter_layers']);
    this.Project_data_inverter_values = summ_data['inverter_layers'];
    this.ortho_file_location = summ_data['ortho_file_location'];
    this.kml_file_location = summ_data['kml_file_location'];
    this.thermal_location = summ_data['thermal_hotspot_location'];
    this.cad_file_location = summ_data['cad_file_location'];
    this.map.removeLayer(this.base_ortho_layer);

    this.base_ortho_layer = L.tileLayer(this.ortho_file_location + '{z}/{x}/{y}.png', {
      center: [this.center],
      minZoom: 2,
      maxZoom: 23,
    }).addTo(this.map);
    // added reload function on changing date
    window.location.reload();
  }
  zoomreset() {
    this.map.setView([this.lat, this.long], this.zoom_level);
  }
  zoomin() {
    this.map.setZoom(this.map.getZoom() + 1);
  }
  zoomout() {
    this.map.setZoom(this.map.getZoom() - 1);
  }
  toggleFullscreen() {
    this.isFullscreen = !this.isFullscreen;
    if (!document.fullscreenElement) {
      const docElm = document.documentElement as HTMLElement;
      if (docElm.requestFullscreen) {
        docElm.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }
  isImage(value: any): boolean {
    return value.match(/\.(jpeg|jpg|png)$/) !== null;
  }
  RemoveKml(mode: string) {
    this.removePopupCard();
    ['accepted', 'accepted1', 'accepted2', 'accepted4', 'accepted5'].forEach(
      f => ((this as any)[f] = false)
    );
    this.subdefects_visibility = 'visibility_off';
    this.grading_visibility = 'visibility_off';
    this.defect_rectify_visibility = 'visibility_off';
    [this.removekml_list, this.removekml_list_inv, this.polies].forEach(list =>
      list.forEach(layer => this.map.removeLayer(layer))
    );
    if (mode !== 'inverter' && this.gb_layer) {
      this.map.removeLayer(this.gb_layer);
    }
  }
  polygonMarkerCreating(coordsArr: string[], color: string, description: string) {
    const pts: any = [];
    for (let i = 0; i < coordsArr.length; i += 3) {
      pts.push({ lat: +coordsArr[i + 1], lng: +coordsArr[i], alt: +coordsArr[i + 2] });
    }
    const poly = L.polygon(pts, { color, weight: 6 }).addTo(this.map);
    this.polies.push(poly);
    poly.on('click', e => {
      this.isModalOpen = false;
      const doc = this.domParser.parseFromString(description, 'text/html');
      const rows = Array.from(doc.getElementsByTagName('tr'));
      this.descObj = {};
      this.descObj_cadestral = {};
      const storageObj = ['cadastral_map', 'Vegetation'].includes(this.current_kml_data)
        ? this.descObj_cadestral
        : this.descObj;

      if (rows.length) {
        rows.forEach(row => {
          const [tdKey, tdVal] = Array.from(row.getElementsByTagName('td'));
          if (!tdKey || !tdVal) return;
          const key = tdKey.innerText.trim().replace(':', '');
          const img = tdVal.querySelector('img');
          const val = img ? img.src : tdVal.innerText.trim();
          if (key === 'Table No') this.table_no = val;
          storageObj[key] = val;
        });
      } else {
        this.descObj_cadestral['Description'] = description;
      }
      delete this.descObj['RGB_Image'];
      delete this.descObj['Thermal_Image'];
      switch (this.current_kml_data) {
        case 'cadastral_map':
        case 'Vegetation':
        case 'cadastral':
          this.popup_opened = true;
          L.popup({ closePopupOnClick: true, autoClose: true })
            .setLatLng(e.latlng)
            .setContent(description)
            .openOn(this.map);
          break;
        case 'Grading':
          this.createDataForGpxfile(this.table_no);
          break;
        default:
          this.popup_card_visibility = true;
          this.popup_card_visibility_cadestral = false;
          this.popupContent = this.descObj;
      }
    });
  }
  removePopupCard() {
    if (this.popup_opened) {
      this.map.closePopup();
      this.popup_opened = false;
    }
  }
  async LoadKml(value: any) {
    this.accepted5 = false;
    this.tablegendselect = false;
    this.currentMenu = value.menu === 'inverter_sub_details' ? 'inverter' : value.menu;
    this.RemoveKml(this.currentMenu);
    this.current_kml_data = value.menu;
    this.summaryState = value.tab;

    localStorage.setItem('kmlfilename', this.summaryState);
    localStorage.setItem('mode', value.menu);
    localStorage.setItem('page', value.pageno);

    const kmlMenus = ['cadastral', 'cadastral_map', 'Grading', 'Vegetation'];
    const defectMenus = ['summary', 'summary_sub_details'];
    const invMenus = ['inverter', 'inverter_sub_details'];
    if (kmlMenus.includes(value.menu)) {
      if (this.gb_layer) this.map.removeLayer(this.gb_layer);
      let layerData: any;
      switch (value.menu) {
        case 'cadastral':
        case 'cadastral_map': {
          const topo = this.summ.processed_data?.[this.date]?.topography_layers;
          layerData =
            topo?.TopographicFeatures?.sub_feature || topo?.['Cadastral Map']?.sub_feature;
          break;
        }
        case 'Grading':
          layerData =
            this.summ.processed_data?.[this.date]?.grading_layers?.GradingFeatures?.sub_feature;
          break;
        case 'Vegetation':
          layerData =
            this.summ.processed_data?.[this.date]?.vegetation_layers?.['Vegetation Features']
              ?.sub_feature;
          break;
      }
      if (!layerData) {
        console.error('Sub-feature not found');
      } else {
        this.subdefects_page_load('visibility_off');
        const paths = (layerData[value.tab]?.kml || '').split(',');
        await Promise.all(paths.map(p => this.fetchAndProcessKml(p, value.color)));
        this.map.fitBounds(this.track.getBounds());
      }
    }
    if (defectMenus.includes(value.menu)) {
      if (this.gb_layer) this.map.removeLayer(this.gb_layer);
      this.subdefects_page_load('visible');
      await this.load_defects(`${this.kml_file_location}GLOBAL/`, this.summaryState.split(','));
    }
    if (invMenus.includes(value.menu)) {
      this.subdefects_page_load('visible');
      const page = value.pageno.toString().padStart(2, '0');
      await this.load_defects(
        `${this.kml_file_location}INVERTER${page}/`,
        this.summaryState.split(',')
      );
    }
    this.close_popup_card();
  }
  private async fetchAndProcessKml(name: string, color: string) {
    try {
      const res = await fetch(`${this.kml_file_location}GLOBAL/${name}.kml`);
      const text = await res.text();
      const xml = this.domParser.parseFromString(text, 'text/xml');
      this.track = new L.KML(xml);
      this.processKmlPlacemarks(xml, color);
    } catch (e) {
      console.error(`Failed ${name}:`, e);
    }
  }
  processKmlPlacemarks(kml: Document, color: string) {
    const marks = Array.from(kml.getElementsByTagName('Placemark'));
    marks.forEach(pm => this.parsePlacemark(pm, color));
  }
  private parsePlacemark(pm: Element, color: string) {
    const desc = pm.querySelector('description')?.textContent || '';
    const coordEl = pm.getElementsByTagName('coordinates')[0];
    if (!coordEl || !desc) return;
    const coords = coordEl.textContent!.replace(/\n/g, ' ').split(/[ ,]+/).filter(Boolean);
    if (this.current_kml_data === 'Grading' && !this.onceCalled) {
      this.defaultchart(desc);
      this.onceCalled = true;
    }
    this.polygonMarkerCreating(
      coords,
      this.extractTableNo(desc) === 'Hotspot' ? 'green' : color,
      desc
    );
  }
  defaultchart(description: string) {
    const td = this.domParser.parseFromString(description, 'text/html').getElementsByTagName('td');
    this.table_no = td[1]?.textContent || '';
    this.createDataForGpxfile(this.table_no);
  }
  async load_defects(url: string, defects: string[]) {
    for (const d of defects) {
      try {
        const res = await fetch(`${url}${d}.kml`);
        const text = await res.text();
        const xml = this.domParser.parseFromString(text, 'text/xml');
        this.track = new L.KML(xml);
        Array.from(xml.getElementsByTagName('Placemark')).forEach(pm =>
          this.parsePlacemark(pm, this.findDefectColor(pm))
        );
        this.popupKml = xml;
      } catch (e) {
        console.error(`Error loading ${d}:`, e);
      }
    }
    localStorage.setItem('kml_popup_node', '');
  }
  private findDefectColor(pm: Element): string {
    const desc = pm.querySelector('description')?.textContent || '';
    const name = this.extractTableNo(desc);
    const key = name.trim().replace(/\s+/g, '_').toLowerCase();
    const entry = this.allDefects.find(
      e => e.name.trim().replace(/\s+/g, '_').toLowerCase() === key
    );
    return entry?.color || 'red';
  }
  private extractTableNo(desc: string): string {
    const tds = Array.from(
      this.domParser.parseFromString(desc, 'text/html').getElementsByTagName('td')
    );
    for (let i = 0; i < tds.length; i += 2) {
      if (tds[i].textContent === 'Defect:') {
        return tds[i + 1]?.textContent || '';
      }
    }
    return '';
  }
  subdefects_page_load(vis: 'visible' | 'visibility_off') {
    this.subdefects_visibility = vis;
    if (vis === 'visible') this._http.setsubdefects({ status: vis });
  }
  async LoadGBKml(page: number) {
    if (this.gb_layer) this.map.removeLayer(this.gb_layer);
    this.removePopupCard();
    const p = page.toString().padStart(2, '0');
    const url = `${this.kml_file_location}INVERTER${p}/gb.kml`;
    try {
      const res = await fetch(url);
      const txt = await res.text();
      const xml = this.domParser.parseFromString(txt, 'text/xml');
      this.gb_layer = new L.KML(xml);
      this.map.addLayer(this.gb_layer);
      this.popupKml = xml;
      this.map.fitBounds(this.gb_layer.getBounds());
    } catch (e) {
      console.error('GB KML load failed:', e);
    }
    this.close_popup_card();
  }
  ViewMenu(option: string) {
    this.tablegendselect = false;
    this.selectedImage = option;
    this.isOpacity = ['thermal', 'DTM', 'slope'].includes(option);

    const config: Record<string, any> = {
      thermal: {
        key: 'thermal',
        loc: this.thermal_location,
        layer: 'thermal_layer',
        flag: 'accepted',
      },
      cad: { key: 'cad', loc: this.cad_file_location, layer: 'cad_layer', flag: 'accepted1' },
      DTM: {
        key: 'DTM',
        loc: this.cad_file_location,
        layer: 'dtm_layer',
        flag: 'accepted2',
        showFlag: 'isShown1',
      },
      slope: {
        key: 'slope',
        loc: this.thermal_location,
        layer: 'slope_layer',
        flag: 'accepted4',
        showFlag: 'isShown2',
      },
    };

    if (config[option]) {
      const c = config[option];
      this.toggleLayer(c.key, c.loc, c.layer, c.flag, c.showFlag);
    } else if (option === 'AllAnomalies') {
      this.toggleKML();
    }
  }
  toggleLayer(key: string, loc: string, layerVar: string, acceptFlag: string, showFlag?: string) {
    if (!loc || loc === 'None') {
      alert(`No ${key.toUpperCase()} for this project`);
      this.selectedImage = 'none';
      this.isOpacity = false;
      (this as any)[acceptFlag] = false;
      return;
    }

    const isOn = localStorage.getItem(key) === key;
    if (isOn) {
      localStorage.setItem(key, 'none');
      (this as any)[layerVar] = L.tileLayer(`${loc}{z}/{x}/{y}.png`, {
        center: [this.center],
        minZoom: 2,
        maxZoom: 23,
        opacity: this.opacity_value,
      }).addTo(this.map);
      (this as any)[acceptFlag] = true;
      if (showFlag) (this as any)[showFlag] = 'visibility';
    } else {
      localStorage.setItem(key, key);
      this.map.removeLayer((this as any)[layerVar]);
      this.selectedImage = 'none';
      this.isOpacity = false;
      (this as any)[acceptFlag] = false;
      if (showFlag) (this as any)[showFlag] = 'visibility_off';
    }
  }
  toggleKML() {
    this.RemoveKml('allanomalies');
    this.accepted5 = !this.accepted5;
    this.selectedImage = this.accepted5 ? 'AllAnomalies' : 'none';
    if (this.accepted5) {
      this.allDefects
        .filter(d => d.kml !== 'NA')
        .forEach(d => this.fetchAndProcessKml(d.kml, d.color));
    }
  }
  async fetchKML(kmlFile: string, color: string) {
    await this.fetchAndProcessKml(kmlFile, color);
    this.map.fitBounds(this.track.getBounds());
  }
  map_view(image: string) {
    this.selectedImage2 = image;
    const addTile = (url: string, opts: any = {}) =>
      L.tileLayer(url, { center: [this.lat, this.long], minZoom: 2, maxZoom: 23, ...opts }).addTo(
        this.map
      );

    if (image === 'satellite') {
      this.satellite_layer = addTile(this.sattelite_tile_layer);
      addTile(this.ortho_file_location + '{z}/{x}/{y}.png');
      [
        ['thermal', 'accepted'],
        ['cad', 'accepted1'],
        ['DTM', 'accepted2'],
      ].forEach(([k, f]) => {
        if (localStorage.getItem(k) === 'none') {
          (this as any)[f] = false;
          localStorage.setItem(k, k);
          this.ViewMenu(k);
        }
      });
    } else if (this.satellite_layer) {
      addTile(this.terrain_tile_layer);
      addTile(this.ortho_file_location + '{z}/{x}/{y}.png');
    }
  }
  close_popup_card() {
    this.popup_card_visibility = false;
    this.popup_card_visibility_cadestral = false;
    this.popup_card_visibility_grading = false;
  }
  subdefects_page(latlong) {
    this.map.setView(new L.LatLng(latlong[0], latlong[1]), 22);
    const marker = L.marker([latlong[0], latlong[1]], {
      icon: L.icon(this.iconOptions),
    }).addTo(this.map);
    const lat = latlong[0];
    const lon = latlong[1];
    marker.bindTooltip(`Latitude: ${lat}<br>Longitude: ${lon}`).openTooltip();
    setTimeout(() => {
      this.map.removeLayer(marker);
    }, 4000);
  }
  getUserLocation() {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;
          const userMarker = L.marker([userLat, userLng], {
            icon: L.icon(this.iconOptions),
          }).addTo(this.map);
          userMarker.bindPopup('Your Current Location').openPopup();
          this.map.flyTo([userLat, userLng], 18);
        },
        error => {
          this.toastr.error(error.message, 'Error Occurred');
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0,
        }
      );
    } else {
      this.toastr.error('Geolocation is not available in this browser.', 'Error Occurred');
    }
  }
  drawAOIPlugin(): void {
    this.drawnItems = new L.FeatureGroup().addTo(this.map);
    this.markerItems = new L.FeatureGroup().addTo(this.map);
    const MyCustomMarker = L.Icon.extend({ options: this.iconOptions });
    this.drawControl = new L.Control.Draw({
      position: 'topright',
      draw: {
        polyline: true,
        polygon: { showArea: true, showLength: true },
        circle: { showArea: true, showLength: true },
        rectangle: { showArea: false, showLength: true },
        marker: { icon: new MyCustomMarker() },
        circlemarker: false,
      },
      edit: {
        featureGroup: this.drawnItems,
        remove: false,
      },
    });
    const getLayerType = (layer: any): string => {
      if (layer instanceof L.Polygon) return 'polygon';
      if (layer instanceof L.Rectangle) return 'rectangle';
      if (layer instanceof L.Circle) return 'circle';
      if (layer instanceof L.Polyline) return 'polyline';
      if (layer instanceof L.Marker) return 'marker';
      return 'unknown';
    };

    this.map.on(L.Draw.Event.CREATED, (event: any) => {
      this.handleLayerCreated(event.layerType, event.layer, 'created');
    });

    this.map.on(L.Draw.Event.EDITED, (event: any) => {
      event.layers.eachLayer((layer: any) => {
        const layerType = getLayerType(layer);
        if (layerType !== 'unknown') {
          this.handleLayerCreated(layerType, layer, 'edited');
        } else {
          console.error('Unhandled layer type in edited event:', layer);
        }
      });
    });
  }
  handleLayerCreated(layerType: string, layer: any, action: string): void {
    let coordinates: any;
    let area: number;

    const calculateArea = (coords: any): number => L.GeometryUtil.geodesicArea(coords);

    const calculatePolylineDistance = (coords: any): number =>
      coords.reduce((total, curr, idx, arr) => {
        if (idx === 0) return 0;
        return total + arr[idx - 1].distanceTo(curr);
      }, 0);

    switch (layerType) {
      case 'polygon':
      case 'rectangle':
        coordinates = layer.getLatLngs()[0];
        area = calculateArea(coordinates);
        break;
      case 'circle':
        coordinates = layer.getLatLng();
        area = Math.PI * Math.pow(layer.getRadius(), 2);
        break;
      case 'polyline':
        coordinates = layer.getLatLngs();
        area = calculatePolylineDistance(coordinates);
        break;
      case 'marker':
        coordinates = layer.getLatLng();
        area = 0;
        break;
      default:
        console.error('Unknown layer type:', layerType);
        return;
    }
    Object.assign(this.drawtool, {
      area: Number(area.toFixed(2)),
      aoi_type: layerType,
      polygon: coordinates,
      label: '',
      description: '',
    });

    const dialogRef = this.dialog.open(AoiDialogComponent, {
      data: { drawtool: this.drawtool },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((result: any) => {
      if (result?.drawtool) {
        Object.assign(this.drawtool, {
          label: result.drawtool.label,
          description: result.drawtool.description,
        });
        if (action == 'created') {
          this.drawtool.id = this.project_id;
          this.drawtool.date = this.date;
          this.sidebarComponent.createDrawtool(this.drawtool);
        } else if (action == 'edited') {
          this.sidebarComponent.updateDrawtool(this.drawtool);
        }
        this.map.removeControl(this.drawControl);
      }
    });
  }
  updateAOIView(event: { action: string; data: any }) {
    const { action, data } = event;
    this.markerItems?.clearLayers();
    if (action !== 'edit') {
      this.drawnItems?.clearLayers();
    } else {
      this.drawtool = data;
    }
    const manageDrawControl = (shouldAdd: boolean) => {
      if (this.drawControl) {
        shouldAdd
          ? this.map.addControl(this.drawControl)
          : this.map.removeControl(this.drawControl);
      }
    };
    switch (action) {
      case 'add':
      case 'edit':
        manageDrawControl(true);
        break;
      case 'remove':
        manageDrawControl(false);
        break;
      case 'show':
        manageDrawControl(false);
        this.renderAOI(data);
        break;
      default:
        console.error(`Unhandled action: ${action}`, event);
    }
  }
  renderAOI(aoi) {
    const addMarkers = (coordinates: any, label: string): void => {
      const markers = L.layerGroup();
      coordinates.forEach(coord => {
        const popupContent = `
        <b>Label</b>: ${label}<br/>
        <b>Latitude</b>: ${coord.lat.toFixed(6)}<br/>
        <b>Longitude</b>: ${coord.lng.toFixed(6)}<br/>
      `;
        const marker = L.marker(coord, {
          icon: L.icon(this.iconOptions),
        });
        marker.bindPopup(popupContent).openPopup();
        markers.addLayer(marker);
      });
      this.markerItems.addLayer(markers);
    };

    if (aoi.aoi_type === 'polygon' || aoi.aoi_type === 'rectangle' || aoi.aoi_type === 'polyline') {
      const polygon = L.polygon(aoi.polygon, {
        color: 'darkcyan',
        weight: 1,
      });
      addMarkers(polygon.getLatLngs()[0], aoi.label);
      this.drawnItems.addLayer(polygon);
      this.map.fitBounds(polygon.getBounds());
    } else if (aoi.aoi_type === 'circle') {
      const circleCenter = L.latLng(aoi.polygon.lat, aoi.polygon.lng);
      const circle = L.circle(circleCenter, {
        color: 'darkcyan',
        radius: Math.sqrt(aoi.area / Math.PI),
      });
      addMarkers([circleCenter], aoi.label);
      this.drawnItems.addLayer(circle);
      this.map.setView(circleCenter, 18);
    } else if (aoi.aoi_type === 'marker') {
      const marker = L.marker(aoi.polygon, {
        icon: L.icon(this.iconOptions),
      });
      const popupContent = `
        <b>Label</b>: ${aoi.label}<br/>
        <b>Latitude</b>: ${aoi.polygon.lat.toFixed(6)}<br/>
        <b>Longitude</b>: ${aoi.polygon.lng.toFixed(6)}<br/>
      `;
      marker.bindPopup(popupContent).openPopup();
      this.drawnItems.addLayer(marker);
      this.map.setView(aoi.polygon, 18);
    }
  }
}
