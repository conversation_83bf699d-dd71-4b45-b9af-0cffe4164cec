import { ApiConfigService } from '@/controller/api-config.service';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { City, Country, State } from 'country-state-city';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.css'],
})
export class SignupComponent implements OnInit {
  signupForm: FormGroup;
  verifyForm: FormGroup;
  hide = true;
  hide2 = true;

  countries: any = [];
  states: any = [];
  cities: any = [];
  states_data: any = [];
  cities_data: any = [];

  countryCode: any;
  data_verify = false;
  Isworking = false;
  isValidPassword = true;

  constructor(
    private router: Router,
    private apiConfigService: ApiConfigService,
    private toastr: ToastrService
  ) {
    this.signupForm = new FormGroup({
      organization: new FormControl('', Validators.required),
      first_name: new FormControl('', Validators.required),
      last_name: new FormControl('', Validators.required),
      contact: new FormControl('', [
        Validators.required,
        Validators.pattern(/^[+]?[0-9]{1,3}[.-\s]?[(]?\d{1,4}[)]?[.-\s]?\d{1,4}[.-\s]?\d{1,9}$/),
      ]),
      email: new FormControl({ value: '', disabled: true }, Validators.required),
      country: new FormControl('', Validators.required),
      state: new FormControl('', Validators.required),
      city: new FormControl('', Validators.required),
      pincode: new FormControl('', [
        Validators.required,
        Validators.pattern(/^\d{6}(?:[-\s]\d{4})?$/),
      ]),
      password: new FormControl('', Validators.required),
      cpassword: new FormControl('', Validators.required),
    });
    this.verifyForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.email]),
    });
  }
  ngOnInit(): void {
    this.countries = Country.getAllCountries();
    this.states = State.getAllStates();
    this.cities = City.getAllCities();
  }
  hasError = (controlast_name: string, errorName: string) => {
    return this.signupForm.controls[controlast_name].hasError(errorName);
  };
  hasError1 = (controlast_name: string, errorName: string) => {
    return this.verifyForm.controls[controlast_name].hasError(errorName);
  };
  applyPattern(controlName: string) {
    const control = this.verifyForm.get(controlName);
    if (control?.value) {
      const transformedValue = control.value.replace(/\s+/g, '').toLowerCase();
      control.setValue(transformedValue, { emitEvent: false });
    }
  }
  trimValue(controlName: string) {
    const control = this.signupForm.get(controlName);
    if (control?.value && typeof control.value === 'string') {
      control.setValue(control.value.trim());
    }
  }
  checkPassword() {
    if (this.signupForm.value.password === this.signupForm.value.cpassword) {
      this.isValidPassword = true;
    } else {
      this.isValidPassword = false;
    }
  }
  verify() {
    this.Isworking = true;
    this.apiConfigService.isEmailUnique(this.verifyForm.getRawValue()).subscribe(
      (res: any) => {
        if (res['status'] == 'success') {
          this.toastr.success(res['message']);
          this.data_verify = true;
          this.Isworking = false;
          this.signupForm.controls['email'].setValue(this.verifyForm.controls['email'].value);
        }
      },
      (err: any) => {
        this.Isworking = false;
        this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
      }
    );
  }
  selectCountry(countryCode: string) {
    this.countryCode = countryCode;
    this.states_data = this.states.filter(state => state.countryCode === countryCode);
  }
  selectState(stateCode: string) {
    this.cities_data = this.cities.filter(
      city => city.stateCode === stateCode && city.countryCode === this.countryCode
    );
  }
  signup() {
    if (this.signupForm.valid && this.isValidPassword) {
      this.Isworking = true;
      this.apiConfigService.register(this.signupForm.getRawValue()).subscribe(
        res => {
          if (res['status'] == 'success') {
            this.Isworking = false;
            this.toastr.success(res['message']);
            this.router.navigate(['/auth', 'login']);
          } else {
            this.Isworking = false;
            this.toastr.error(res['message']);
          }
        },
        err => {
          this.Isworking = false;
          this.toastr.error('Please Try Again or Contact Admin', 'Error Occured');
          console.error(err);
        }
      );
    } else {
      this.toastr.error('Invalid Form Details');
    }
  }
}
