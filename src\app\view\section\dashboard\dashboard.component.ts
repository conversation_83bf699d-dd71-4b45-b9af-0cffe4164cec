import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
})
export class DashboardComponent implements OnInit {
  no_project = environment.no_project;
  get_dashboard_data: any = null;
  main_data: any = null;
  noDataFound: boolean = false;

  constructor(
    private sharedDataService: SharedDataService,
    private apiConfigService: ApiConfigService,
    private userService: UserService
  ) {}

  ngOnInit() {
    this.sharedDataService
      .fetchData(this.apiConfigService.getUserDetails(), 'user_data')
      .subscribe(userData => {
        if (userData) {
          this.userService.setUserData(
            userData['company']['id'],
            userData['role'],
            userData['sidebar_logo']
          );
          this.sharedDataService
            .fetchData(this.apiConfigService.getDashboardData(), 'get_dashboard_data')
            .subscribe(dashboardData => {
              this.get_dashboard_data = Object.keys(dashboardData).length ? dashboardData : null;
              this.sharedDataService
                .fetchData(this.apiConfigService.getAllProjects({ count: 3 }), 'get_all_projects')
                .subscribe(projectData => {
                  this.main_data = projectData.length ? projectData : [];
                  this.noDataFound = !this.get_dashboard_data && this.main_data.length === 0;
                });
            });
        }
      });
  }
}
