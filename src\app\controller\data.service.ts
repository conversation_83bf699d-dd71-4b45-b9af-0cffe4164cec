import { Injectable } from '@angular/core';
import { AES, enc, lib, mode, pad } from 'crypto-js';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DataService {
  private readonly masterKey: string = enc.Hex.parse(environment.master_key);
  private readonly masterKeyString: string = enc.Hex.parse(environment.master_key).toString();
  private readonly KEY_KEY = 'key';
  private readonly IV_KEY = 'iv';
  private keyBase64: string;
  private ivHex: string;

  generateKey(): void {
    this.keyBase64 = this.retrieveKey(localStorage.getItem(this.KEY_KEY));
    this.ivHex = this.retrieveKey(localStorage.getItem(this.IV_KEY));
    if (!this.keyBase64 || !this.ivHex) {
      this.keyBase64 = enc.Base64.stringify(lib.WordArray.random(32));
      this.ivHex = enc.Hex.stringify(lib.WordArray.random(16));
      this.storeKey(this.KEY_KEY, this.keyBase64);
      this.storeKey(this.IV_KEY, this.ivHex);
    }
  }

  retrieveKey(encryptedData: string): string {
    if (!encryptedData) return null;
    try {
      return AES.decrypt(encryptedData, this.masterKeyString).toString(enc.Utf8);
    } catch (error) {
      console.error('Error decrypting key:', error);
      return null;
    }
  }

  storeKey(storageKey: string, data: string): void {
    try {
      localStorage.setItem(storageKey, AES.encrypt(data, this.masterKeyString).toString());
    } catch (error) {
      console.error('Error storing key:', error);
    }
  }

  encryptData(data: any): string {
    try {
      return AES.encrypt(JSON.stringify(data), enc.Base64.parse(this.keyBase64), {
        iv: enc.Hex.parse(this.ivHex),
        padding: pad.Pkcs7,
        mode: mode.CBC,
      }).toString();
    } catch (error) {
      console.error('Error during encryption:', error);
      throw error;
    }
  }

  decryptData(encryptedData: string): any {
    try {
      const bytes = AES.decrypt(encryptedData, enc.Base64.parse(this.keyBase64), {
        iv: enc.Hex.parse(this.ivHex),
        padding: pad.Pkcs7,
        mode: mode.CBC,
      });
      return JSON.parse(bytes.toString(enc.Utf8));
    } catch (error) {
      console.error('Error during decryption:', error);
      throw error;
    }
  }

  encryptString(message: string): string {
    try {
      const iv = lib.WordArray.random(16);
      const encrypted = AES.encrypt(enc.Utf8.parse(message), this.masterKey, {
        iv: iv,
        padding: pad.Pkcs7,
        mode: mode.CBC,
      });
      return `${encrypted.toString()}:${iv.toString(enc.Base64)}`;
    } catch (error) {
      console.error('Error during string encryption:', error);
      throw error;
    }
  }

  decryptString(encryptedMessage: string): string {
    try {
      const [ciphertext, iv] = encryptedMessage.split(':');
      const decrypted = AES.decrypt(ciphertext, this.masterKey, {
        iv: enc.Base64.parse(iv),
        padding: pad.Pkcs7,
        mode: mode.CBC,
      });
      return decrypted.toString(enc.Utf8);
    } catch (error) {
      console.error('Error during string decryption:', error);
      throw error;
    }
  }
}
