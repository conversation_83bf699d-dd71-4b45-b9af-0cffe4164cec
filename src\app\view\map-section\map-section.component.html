<sidebar
  *ngIf="sidebarVisible"
  (current_summary_state_event)="LoadKml($event)"
  (inverter_page_event)="LoadGBKml($event)"
  (removing_kml_event)="RemoveKml($event)"
  (subdefects_page_event)="subdefects_page_load($event)"
  (cadastrial_map_page_event)="LoadKml($event)"
  (close_stack)="closeStack()"
  (aoi_action_event)="updateAOIView($event)"
  [booleanValueFromParent]="storedDateValue"></sidebar>
<subdefects
  *ngIf="subdefects_visibility == 'visible'; else visibility_off"
  (subdefects_page_event)="subdefects_page($event)"></subdefects>
<ng-template #visibility_off></ng-template>
<ng-template #grading_visibility_off></ng-template>
<div #mapContainer class="map-container"></div>
<button class="toggle-button toggle-right" (click)="isOpen = !isOpen" [ngClass]="{ off: !isOpen }">
  <mat-icon *ngIf="isOpen">keyboard_arrow_right</mat-icon>
  <mat-icon *ngIf="!isOpen">keyboard_arrow_left</mat-icon>
</button>
<button
  class="toggle-button toggle-left"
  (click)="sidebarVisible = !sidebarVisible"
  [ngClass]="{ off: !sidebarVisible }">
  <mat-icon *ngIf="sidebarVisible">keyboard_arrow_left</mat-icon>
  <mat-icon *ngIf="!sidebarVisible">keyboard_arrow_right</mat-icon>
</button>
<div class="tool-sidebar" [ngClass]="{ open: isOpen }">
  <div class="sidebar-menu">
    <mat-nav-list>
      <mat-list-item (click)="toggleFullscreen()" *ngIf="isFullscreen">
        <mat-icon matTooltip="Close Fullscreen">close_fullscreen</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="toggleFullscreen()" *ngIf="!isFullscreen">
        <mat-icon matTooltip="Open Fullscreen">open_in_full</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="zoomreset()">
        <mat-icon matTooltip="Zoom Reset">find_replace</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="zoomin()">
        <mat-icon matTooltip="Zoom In">zoom_in</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="zoomout()">
        <mat-icon matTooltip="Zoom Out">zoom_out</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="openModal(isModalOpen)">
        <mat-icon matTooltip="Open Layers">layers</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="opacityStatus = !opacityStatus" *ngIf="isOpacity">
        <mat-icon matTooltip="Change Opacity">opacity</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="tablegendselect = !tablegendselect" *ngIf="accepted5">
        <mat-icon matTooltip="Legend View">list</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="exportMap()">
        <mat-icon matTooltip="Export Map">print</mat-icon>
      </mat-list-item>
      <mat-list-item (click)="weather_show = !weather_show">
        <mat-icon matTooltip="Weather">sunny</mat-icon>
      </mat-list-item>
      <!-- <mat-list-item (click)="getUserLocation()">
        <mat-icon matTooltip="Your current location">location_on</mat-icon>
      </mat-list-item> -->
      <mat-list-item (click)="sidebarVisible = !sidebarVisible" id="infs">
        <mat-icon [matTooltip]="sidebarVisible ? 'Full Screen View' : 'Exit Full Screen View'">
          fullscreen
        </mat-icon>
      </mat-list-item>
    </mat-nav-list>
  </div>
</div>
<div class="mapslayout" *ngIf="isModalOpen">
  <ng-container *ngIf="Project_data_thermo != ''">
    <div class="drone-data-thermal">
      <span class="span-font">Drone Data</span>
      <div class="base-map-img">
        <ng-container *appHasRole="['admin', 'manager']">
          <div class="img-items">
            <img
              [src]="Thermal"
              (click)="ViewMenu('thermal')"
              class="img-dim"
              [style.border-color]="selectedImage === 'thermal' ? 'darkcyan' : 'white'" />
            <span class="span-font">Thermal</span>
          </div>
          <div class="img-items">
            <img
              [src]="Slope"
              (click)="ViewMenu('AllAnomalies')"
              class="img-dim"
              [style.border-color]="selectedImage === 'AllAnomalies' ? 'darkcyan' : 'white'" />
            <span class="span-font">Anomalies</span>
          </div>
        </ng-container>
        <div class="img-items">
          <img
            [src]="CAD"
            (click)="ViewMenu('cad')"
            class="img-dim"
            [style.border-color]="selectedImage === 'cad' ? 'darkcyan' : 'white'" />
          <span class="span-font">CAD</span>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="Project_data_grad != ''">
    <div class="base-map">
      <span class="span-font">Drone Data</span>
      <div class="base-map-img">
        <ng-container *appHasRole="['admin', 'manager']">
          <div class="img-items">
            <img
              [src]="Orthomosaic"
              (click)="ViewMenu('Raw')"
              class="img-dim"
              [style.border-color]="selectedImage === 'Raw' ? 'darkcyan' : 'white'" />
            <span class="span-font">OrthoM</span>
          </div>
          <div class="img-items">
            <img
              [src]="DTM"
              (click)="ViewMenu('DTM')"
              class="img-dim"
              [style.border-color]="selectedImage === 'DTM' ? 'darkcyan' : 'white'" />
            <span class="span-font">DTM</span>
          </div>
          <div class="img-items">
            <img
              [src]="Slope"
              (click)="ViewMenu('slope')"
              class="img-dim"
              [style.border-color]="selectedImage === 'slope' ? 'darkcyan' : 'white'" />
            <span class="span-font">Slope</span>
          </div>
        </ng-container>
        <div class="img-items" *ngIf="cad_val">
          <img
            [src]="CAD"
            (click)="ViewMenu('cad')"
            class="img-dim"
            [style.border-color]="selectedImage === 'cad' ? 'darkcyan' : 'white'" />
          <span class="span-font">CAD</span>
        </div>
      </div>
    </div>
  </ng-container>
  <div class="base-map">
    <span class="span-font">Base Map</span>
    <div class="base-map-img">
      <div class="img-items">
        <img
          [src]="default"
          (click)="map_view('terrain')"
          class="img-dim"
          [style.border-color]="selectedImage2 === 'terrain' ? 'darkcyan' : 'white'" />
        <span class="span-font">Terrain</span>
      </div>
      <div class="img-items">
        <img
          [src]="satellite"
          (click)="map_view('satellite')"
          class="img-dim"
          [style.border-color]="selectedImage2 === 'satellite' ? 'darkcyan' : 'white'" />
        <span class="span-font">Satellite</span>
      </div>
    </div>
  </div>
</div>
<div class="dtmLegend" cdkDrag *ngIf="isShown1 == 'visibility'">
  <div class="dtmscale" style="right: 105px">
    <div class="refnaming">775 m</div>
    <div class="refnaming">770 m</div>
    <div class="refnaming">765 m</div>
    <div class="refnaming">760 m</div>
    <div class="refnaming">755 m</div>
    <div class="refnaming">750 m</div>
    <div class="refnaming">745 m</div>
    <div class="refnaming">741 m</div>
  </div>
  <div class="part2">
    <div class="dtmscale1">
      <div class="refname" style="border-top: solid 1.5px black"></div>
      <div class="refname"></div>
      <div class="refname"></div>
      <div class="refname"></div>
      <div class="refname"></div>
      <div class="refname"></div>
      <div class="refname"></div>
    </div>
    <div class="dtmgradient"></div>
  </div>
</div>
<div class="slopeLegend" cdkDrag *ngIf="isShown2 == 'visibility'">
  <div class="slopebox">
    <div class="heading" style="width: 20px"></div>
    <div class="refslope" style="background-color: white"></div>
    <div class="refslope" style="background-color: darkmagenta"></div>
    <div class="refslope" style="background-color: navy"></div>
    <div class="refslope" style="background-color: blue"></div>
    <div class="refslope" style="background-color: skyblue"></div>
    <div class="refslope" style="background-color: green"></div>
    <div class="refslope" style="background-color: yellow"></div>
    <div class="refslope" style="background-color: rgb(239, 199, 99)"></div>
    <div class="refslope" style="background-color: orange"></div>
    <div class="refslope" style="background-color: maroon"></div>
  </div>
  <div class="slopebox2">
    <div class="heading" style="width: 50px">Degree</div>
    <div class="refslopenaming">0-0.5</div>
    <div class="refslopenaming">0.5-1</div>
    <div class="refslopenaming">1-1.5</div>
    <div class="refslopenaming">1.5-2</div>
    <div class="refslopenaming">2-3</div>
    <div class="refslopenaming">3-4</div>
    <div class="refslopenaming">4-5</div>
    <div class="refslopenaming">5-6</div>
    <div class="refslopenaming">6-10</div>
    <div class="refslopenaming" style="width: fit-content">Above 10</div>
  </div>
</div>
<div *ngIf="opacityStatus">
  <div class="wrapper">
    <div class="opacity-head">
      <mat-icon id="select-all-icon">select_all</mat-icon>
      <span class="opacityhead">Change Opacity</span>
    </div>
    <input
      type="range"
      min="0"
      step="0.1"
      max="1"
      value="0.5"
      id="myRange"
      (input)="handleRangeChange($event)" />
  </div>
</div>
<div class="exam">
  <marquee direction="left" scrollamount="5" class="header" *ngIf="isOpen">
    {{ project_name }}
  </marquee>
  <div class="exam2" *ngIf="isOpen">
    <mat-select class="width-set" name="date" [(ngModel)]="date" (ngModelChange)="Datemenu(date)">
      <mat-option
        [value]="date"
        *ngFor="let date of Completed_date_array"
        (click)="close_popup_card()">
        {{ date }}
      </mat-option>
    </mat-select>
  </div>
</div>
<div *ngIf="popupDesc == ' '; else elseBlock">
  <mat-card
    [style.visibility]="popup_card_visibility ? 'visible' : 'hidden'"
    style="
      margin-top: 50px;
      width: 25%;
      position: fixed;
      top: 0px;
      right: 70px;
      z-index: 1000;
      border-radius: 10px;
    ">
    <mat-card-content>Click on a feature to view details</mat-card-content>
  </mat-card>
</div>
<ng-template #elseBlock>
  <mat-card
    [style.visibility]="popup_card_visibility ? 'visible' : 'hidden'"
    class="popup-card-error">
    <mat-card-header class="p-0">
      <div
        class="d-flex justify-content-between align-items-center w-100 mx-3 mb-2"
        style="border-bottom: 2px dashed #282828">
        <h2 class="m-0 font-weight-bold">Defect Details</h2>
        <button mat-icon-button (click)="close_popup_card()"><mat-icon>close</mat-icon></button>
      </div>
    </mat-card-header>
    <mat-card-content style="padding: 0px; font-size: 12px">
      <div class="row m-1" *ngFor="let item of popupContent | keyvalue">
        <div class="col-6 px-1">
          <p style="font-weight: 600" class="m-0">{{ item.key }}</p>
        </div>
        <div class="col-6 px-1" style="word-break: break-all">
          <ng-container *ngIf="isImage(item.value)">
            <img style="width: 75px; height: 75px" mat-card-image [src]="item.value" />
          </ng-container>
          <ng-container *ngIf="!isImage(item.value)">{{ item.value }}</ng-container>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</ng-template>
<div *ngIf="popupDesc == ' '; else elseBlock_cadestral">
  <mat-card
    [style.visibility]="popup_card_visibility_cadestral ? 'visible' : 'hidden'"
    style="
      margin-left: 4rem;
      margin-top: 75px;
      width: 16%;
      height: 70px;
      position: fixed;
      top: 0;
      right: 70px;
      z-index: 1000;
    ">
    <b style="margin: 2rem">
      <mat-card-content style="height: 40%">Click on a feature to view details</mat-card-content>
    </b>
  </mat-card>
</div>
<ng-template #elseBlock_cadestral>
  <mat-card
    [style.visibility]="popup_card_visibility_cadestral ? 'visible' : 'hidden'"
    style="
      margin-left: 4rem;
      margin-top: 75px;
      width: 24%;
      height: auto;
      position: fixed;
      top: 0;
      right: 70px;
      z-index: 1000;
      border-radius: 10px;
    ">
    <mat-card-header>
      <div style="margin-left: auto; margin-top: -10px">
        <button mat-icon-button>
          <mat-icon (click)="close_popup_card()" class="pointer">close</mat-icon>
        </button>
      </div>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="Survey_No; else empty_value">
        <div class="row" *ngIf="Survey_No">
          <div class="col-3"><b>Survey No :</b></div>
          <div class="col-8">{{ Survey_No }}</div>
        </div>
        <br />
        <div class="row" *ngIf="Description_cadestral">
          <div class="col-3"><b>Description :</b></div>
          <div class="col-8">{{ Description_cadestral }}</div>
        </div>
        <br />
        <div class="row" *ngIf="Document">
          <div class="col-3"><b>Document :</b></div>
          <div class="col-8">
            <a style="padding-left: 10px; width: 80px" href="{{ Document_link }}">{{ Document }}</a>
          </div>
        </div>
      </div>
      <ng-template #empty_value>
        <div class="row" *ngIf="Description_cadestral">
          <div class="col-2"></div>
          <div class="col-8">{{ Description_cadestral }}</div>
          <div class="col-2"></div>
        </div>
      </ng-template>
      <br />
      <br />
    </mat-card-content>
  </mat-card>
</ng-template>
<div *ngIf="popupDesc == ' '; else elseBlock_grading"></div>
<ng-template #elseBlock_grading>
  <mat-card
    [style.visibility]="popup_card_visibility_grading ? 'visible' : 'hidden'"
    style="
      margin-left: 4rem;
      margin-top: 75px;
      width: 24%;
      height: auto;
      position: fixed;
      top: 0;
      right: 70px;
      z-index: 1000;
      border-radius: 10px;
    ">
    <mat-card-header>
      <div style="margin-left: auto; margin-top: -10px">
        <button mat-icon-button>
          <mat-icon (click)="close_popup_card()" class="pointer">close</mat-icon>
        </button>
      </div>
    </mat-card-header>
    <mat-card-content>
      <div class="row">
        <div class="col-3"><b>Table No :</b></div>
        <div class="col-8">{{ table_number }}</div>
      </div>
      <br />
    </mat-card-content>
  </mat-card>
</ng-template>
<div *ngIf="tablegendselect">
  <mat-card class="legendDefects" cdkDrag>
    <mat-card-content>
      <div *ngFor="let defect of allDefects" style="display: flex; align-items: center">
        <div
          [style.background-color]="defect.color"
          style="margin-right: 8px; border-radius: 10px; width: 13px; height: 13px"></div>
        <span>{{ defect.name }}</span>
      </div>
    </mat-card-content>
  </mat-card>
</div>
<div class="weatherlayout" *ngIf="weather_show">
  <app-weather [lat]="lat" [lon]="long"></app-weather>
</div>
<div id="print-template" class="print-only">
  <div class="print-header">
    <div class="header-content">
      <div class="text-content">
        <p class="mb-2">Project Name:{{ project_name }}</p>
        <p>Flight Date:{{ date }}</p>
      </div>
      <img src="{{ logo }}" alt="Logo" class="logo" />
    </div>
  </div>
  <div class="print-map-container"><div id="print-map"></div></div>
  <div class="print-footer">
    <p>Generated by © Datasee.AI at{{ currentTime }}</p>
  </div>
</div>
